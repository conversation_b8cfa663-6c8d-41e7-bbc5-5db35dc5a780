/**
 * 个人事务管理表单保存触发器
 * 在表单保存时自动创建提醒记录
 * 
 * 使用方法：
 * 1. 在个人事务管理表单的保存事件中调用 onPersonalAffairsSave(doc)
 * 2. 或者在工作流的相关节点中调用此函数
 */

// 引入必要的工具函数
// #include "SysUserUtils";

/**
 * 个人事务管理表单保存事件处理函数
 * @param {Object} doc - 保存的文档对象
 */
function onPersonalAffairsSave(doc) {
    try {
        // 验证文档对象
        if (!doc) {
            debug("文档对象为空，无法创建提醒记录");
            return;
        }
        
        // 检查是否是个人事务管理表
        var formName = doc.getFormname();
        if (formName !== "tlk_psm_01_personal_affairs_management") {
            debug("当前表单不是个人事务管理表: " + formName);
            return;
        }
        
        // 检查是否已存在提醒记录（避免重复创建）
        if (hasExistingReminder(doc.getId())) {
            debug("已存在提醒记录，跳过创建");
            return;
        }
        
        // 创建提醒记录
        createPersonalAffairsReminder(doc);
        
    } catch (e) {
        debug("处理个人事务保存事件时发生错误: " + e.message);
        Packages.java.lang.System.out.println("Error in onPersonalAffairsSave: " + e);
    }
}

/**
 * 创建个人事务提醒记录
 * @param {Object} sourceDoc - 源文档（个人事务管理）
 */
function createPersonalAffairsReminder(sourceDoc) {
    try {
        // 获取系统对象
        var user = GetAssistant();
        var applicationId = getApplication();
        var docProcess = getDocProcess(applicationId);
        
        // 生成提醒记录编号
        var reminderPrefix = "PSMT-";
        var reminderBillNo = countNext2(reminderPrefix, true, true, true, 4);
        
        // 获取提醒列表表单
        var listFormName = "tlk_psm_01_personal_affairs_management_list";
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var listForm = formProcess.doViewByFormName(listFormName, applicationId);
        
        if (!listForm) {
            debug("未找到提醒列表表单: " + listFormName);
            return;
        }
        
        // 创建新的提醒记录
        var params = createParamsTable();
        var reminderDoc = docProcess.doNew(listForm, user, params);
        
        // 设置基本信息
        reminderDoc.addStringItem("编号", reminderBillNo);
        reminderDoc.addStringItem("创建人", user.getName());
        reminderDoc.addDateItem("创建日期", getToday());
        
        // 设置关联信息
        reminderDoc.addStringItem("关联事务ID", sourceDoc.getId());
        reminderDoc.addStringItem("关联事务编号", getFieldValue(sourceDoc, "编号"));
        
        // 复制事务信息
        copyAffairsFields(reminderDoc, sourceDoc);
        
        // 设置提醒相关字段
        setReminderSettings(reminderDoc, sourceDoc);
        
        // 创建文档
        docProcess.doCreate(reminderDoc);
        debug("成功创建个人事务提醒记录: " + reminderBillNo);
        
        // 可选：启动提醒流程
        // startReminderWorkflow(reminderBillNo, user);
        
    } catch (e) {
        debug("创建个人事务提醒记录失败: " + e.message);
        throw e;
    }
}

/**
 * 复制事务字段信息
 * @param {Object} reminderDoc - 提醒文档
 * @param {Object} sourceDoc - 源文档
 */
function copyAffairsFields(reminderDoc, sourceDoc) {
    try {
        // 事务标题
        var title = getFieldValue(sourceDoc, "事务标题") || 
                   getFieldValue(sourceDoc, "标题") || 
                   getFieldValue(sourceDoc, "事务名称") || 
                   "个人事务提醒";
        reminderDoc.addStringItem("事务标题", title);
        
        // 事务类型
        var type = getFieldValue(sourceDoc, "事务类型") || "一般事务";
        reminderDoc.addStringItem("事务类型", type);
        
        // 优先级
        var priority = getFieldValue(sourceDoc, "优先级") || "普通";
        reminderDoc.addStringItem("优先级", priority);
        
        // 事务描述
        var description = getFieldValue(sourceDoc, "事务描述") || 
                         getFieldValue(sourceDoc, "备注") || 
                         getFieldValue(sourceDoc, "说明") || "";
        reminderDoc.addStringItem("事务描述", description);
        
        // 负责人
        var responsible = getFieldValue(sourceDoc, "负责人") || 
                         getFieldValue(sourceDoc, "经办人") || 
                         getFieldValue(sourceDoc, "处理人");
        if (responsible) {
            reminderDoc.addStringItem("负责人", responsible);
        }
        
        // 部门
        var department = getFieldValue(sourceDoc, "部门") || getFieldValue(sourceDoc, "所属部门");
        if (department) {
            reminderDoc.addStringItem("部门", department);
        }
        
        debug("事务字段复制完成");
        
    } catch (e) {
        debug("复制事务字段时发生错误: " + e.message);
        throw e;
    }
}

/**
 * 设置提醒相关配置
 * @param {Object} reminderDoc - 提醒文档
 * @param {Object} sourceDoc - 源文档
 */
function setReminderSettings(reminderDoc, sourceDoc) {
    try {
        // 计划完成时间
        var planDate = getFieldValueAsDate(sourceDoc, "计划完成时间") || 
                      getFieldValueAsDate(sourceDoc, "截止时间") ||
                      getFieldValueAsDate(sourceDoc, "完成日期");
        
        if (planDate) {
            reminderDoc.addDateItem("计划完成时间", planDate);
            // 设置提醒时间（提前1天）
            var reminderDate = adjustDay(planDate, -1);
            reminderDoc.addDateItem("提醒时间", reminderDate);
        } else {
            // 如果没有计划完成时间，设置默认提醒时间（明天）
            var defaultReminderDate = adjustDay(getToday(), 1);
            reminderDoc.addDateItem("提醒时间", defaultReminderDate);
        }
        
        // 提醒状态
        reminderDoc.addStringItem("提醒状态", "待提醒");
        
        // 是否已提醒
        reminderDoc.addStringItem("是否已提醒", "否");
        
        // 提醒方式
        reminderDoc.addStringItem("提醒方式", "系统通知");
        
        // 提醒次数
        reminderDoc.addDoubleItem("提醒次数", 0);
        
        debug("提醒设置配置完成");
        
    } catch (e) {
        debug("设置提醒配置时发生错误: " + e.message);
        throw e;
    }
}

/**
 * 检查是否已存在提醒记录
 * @param {string} sourceDocId - 源文档ID
 * @returns {boolean} 是否存在
 */
function hasExistingReminder(sourceDocId) {
    try {
        var sql = "SELECT COUNT(*) as count FROM tlk_psm_01_personal_affairs_management_list " +
                 "WHERE ITEM_关联事务ID = '" + sourceDocId + "'";
        var data = MagicFindBySql("magic", sql);
        return data && data.get("count") > 0;
    } catch (e) {
        debug("检查已存在提醒记录失败: " + e.message);
        return false;
    }
}

/**
 * 安全获取字段值
 * @param {Object} doc - 文档对象
 * @param {string} fieldName - 字段名
 * @returns {string} 字段值
 */
function getFieldValue(doc, fieldName) {
    try {
        return doc.getItemValueAsString(fieldName) || "";
    } catch (e) {
        return "";
    }
}

/**
 * 安全获取日期字段值
 * @param {Object} doc - 文档对象
 * @param {string} fieldName - 字段名
 * @returns {Date} 日期值
 */
function getFieldValueAsDate(doc, fieldName) {
    try {
        return doc.getItemValueAsDate(fieldName);
    } catch (e) {
        return null;
    }
}
