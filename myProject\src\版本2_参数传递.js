#include "SysUserUtils";

// 版本2：通过参数传递文档对象
function createReminderRecord(sourceDoc) {
    try {
        // 检查当前文档是否为个人事务管理表
        if (!sourceDoc || sourceDoc.getFormname() !== "tlk_psm_01_personal_affairs_management") {
            return;
        }
        
        // 获取系统对象
        var user = GetAssistant();
        var applicationId = getApplication();
        var docProcess = getDocProcess(applicationId);
        
        // 生成编号
        var billNo = countNext2("PSMT-", true, true, true, 4);
        
        // 获取list表单
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var listForm = formProcess.doViewByFormName("tlk_psm_01_personal_affairs_management_list", applicationId);
        
        // 创建新记录
        var params = createParamsTable();
        var listDoc = docProcess.doNew(listForm, user, params);
        
        // 根据截图字段设置值
        listDoc.addStringItem("编号", billNo);
        
        // 事务标题 - 从原表复制
        var title = sourceDoc.getItemValueAsString("事务标题") || 
                   sourceDoc.getItemValueAsString("标题") || 
                   sourceDoc.getItemValueAsString("事务名称") || "个人事务";
        listDoc.addStringItem("事务标题", title);
        
        // 内容 - 可以是事务描述或备注
        var content = sourceDoc.getItemValueAsString("事务描述") || 
                     sourceDoc.getItemValueAsString("备注") || 
                     sourceDoc.getItemValueAsString("内容") || 
                     sourceDoc.getItemValueAsString("说明") || "";
        listDoc.addStringItem("内容", content);
        
        // 合规提醒 - 可以根据业务逻辑设置
        var compliance = sourceDoc.getItemValueAsString("合规提醒") || "是";
        listDoc.addStringItem("合规提醒", compliance);
        
        // 创建记录
        docProcess.doCreate(listDoc);
        debug("成功创建提醒记录: " + billNo);
        
    } catch (e) {
        // 错误不影响原始保存
        debug("创建list记录失败: " + e.message);
    }
}

// 在表单保存事件中调用：createReminderRecord(当前文档对象);

#include "SysUserUtils";

(function (){
    var user = GetAssistant();
    var applicationId = getApplication();//获取applicationId                            
    var docProcess = getDocProcess(applicationId);                            
    var orderMainDoc;                                
    //创建采购订单                            
    var cgdPrefix="FEAC-";                              
    var cgBillNo = countNext2(cgdPrefix,true,true,true,4);                             
    var billFormName = "GWFE_01_Apply_Calculate";                            
    var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();                            
    var Form = formProcess.doViewByFormName(billFormName, applicationId);
    var params = createParamsTable();                            
    var doc = docProcess.doNew(Form, user, params);                            
    doc.addStringItem("编号",cgBillNo);                            
    doc.addStringItem("填写人", user.getName());
    doc.addDateItem("填写日期", getToday());
    docProcess.doCreate(doc);//创建                            
    //重新获取采购订单对象                            
    doc = LoadByIndexFieldValue("tlk_gwfe_01_apply_calculate", "ITEM_编号", cgBillNo, true);                              
    var flowid = "__klySHS2JvHgnWWaXel6";                      
    var billParams = createParamsTable();                                           
    billParams.setParameter("_flowid", flowid);                            
    docProcess.doStartFlowOrUpdate(doc, billParams, user); //启动流程 
})()
