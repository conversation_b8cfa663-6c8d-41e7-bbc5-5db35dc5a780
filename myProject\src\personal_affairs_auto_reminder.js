#include "SysUserUtils";

/**
 * 个人事务管理自动提醒功能
 * 当编辑保存 tlk_psm_01_personal_affairs_management 表的内容时，
 * 自动在 tlk_psm_01_personal_affairs_management_list 表中创建事务提醒记录
 */

(function() {
    try {
        // 获取当前用户和应用信息
        var user = GetAssistant();
        var applicationId = getApplication(); // 获取applicationId
        var docProcess = getDocProcess(applicationId);
        
        // 获取当前编辑的个人事务管理文档
        var currentDoc = getCurrentDocument(); // 假设这是获取当前文档的方法
        
        if (!currentDoc) {
            debug("未找到当前编辑的文档");
            return;
        }
        
        // 检查是否是个人事务管理表
        if (currentDoc.getFormname() !== "tlk_psm_01_personal_affairs_management") {
            debug("当前文档不是个人事务管理表，跳过自动提醒创建");
            return;
        }
        
        // 创建事务提醒记录
        createReminderRecord(docProcess, user, currentDoc, applicationId);
        
    } catch (e) {
        debug("创建事务提醒记录时发生错误: " + e.message);
        Packages.java.lang.System.out.println("Error in personal affairs auto reminder: " + e);
    }
})();

/**
 * 创建事务提醒记录
 * @param {Object} docProcess - 文档处理对象
 * @param {Object} user - 当前用户
 * @param {Object} sourceDoc - 源文档（个人事务管理）
 * @param {string} applicationId - 应用ID
 */
function createReminderRecord(docProcess, user, sourceDoc, applicationId) {
    try {
        // 生成提醒记录编号
        var reminderPrefix = "PSMT-";
        var reminderBillNo = countNext2(reminderPrefix, true, true, true, 4);
        
        // 获取事务提醒表单
        var billFormName = "tlk_psm_01_personal_affairs_management_list";
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var Form = formProcess.doViewByFormName(billFormName, applicationId);
        
        if (!Form) {
            debug("未找到表单: " + billFormName);
            return;
        }
        
        // 创建新的提醒记录
        var params = createParamsTable();
        var reminderDoc = docProcess.doNew(Form, user, params);
        
        // 设置提醒记录的基本信息
        reminderDoc.addStringItem("编号", reminderBillNo);
        reminderDoc.addStringItem("创建人", user.getName());
        reminderDoc.addDateItem("创建日期", getToday());
        
        // 从源文档获取相关信息并设置到提醒记录
        setReminderFields(reminderDoc, sourceDoc);
        
        // 设置关联关系
        if (sourceDoc.getId()) {
            reminderDoc.addStringItem("关联事务ID", sourceDoc.getId());
            reminderDoc.addStringItem("关联事务编号", sourceDoc.getItemValueAsString("编号"));
        }
        
        // 创建提醒记录
        docProcess.doCreate(reminderDoc);
        debug("成功创建事务提醒记录: " + reminderBillNo);
        
        // 可选：启动提醒流程
        startReminderFlow(docProcess, reminderDoc, reminderBillNo, user);
        
    } catch (e) {
        debug("创建提醒记录失败: " + e.message);
        throw e;
    }
}

/**
 * 设置提醒记录的字段值
 * @param {Object} reminderDoc - 提醒记录文档
 * @param {Object} sourceDoc - 源文档
 */
function setReminderFields(reminderDoc, sourceDoc) {
    try {
        // 事务标题
        var title = sourceDoc.getItemValueAsString("事务标题") || sourceDoc.getItemValueAsString("标题") || "个人事务提醒";
        reminderDoc.addStringItem("事务标题", title);
        
        // 事务类型
        var type = sourceDoc.getItemValueAsString("事务类型") || "一般事务";
        reminderDoc.addStringItem("事务类型", type);
        
        // 优先级
        var priority = sourceDoc.getItemValueAsString("优先级") || "普通";
        reminderDoc.addStringItem("优先级", priority);
        
        // 事务描述
        var description = sourceDoc.getItemValueAsString("事务描述") || sourceDoc.getItemValueAsString("备注") || "";
        reminderDoc.addStringItem("事务描述", description);
        
        // 计划完成时间
        var planDate = sourceDoc.getItemValueAsDate("计划完成时间");
        if (planDate) {
            reminderDoc.addDateItem("计划完成时间", planDate);
            // 设置提醒时间（提前1天）
            var reminderDate = adjustDay(planDate, -1);
            reminderDoc.addDateItem("提醒时间", reminderDate);
        } else {
            // 如果没有计划完成时间，设置默认提醒时间（明天）
            var defaultReminderDate = adjustDay(getToday(), 1);
            reminderDoc.addDateItem("提醒时间", defaultReminderDate);
        }
        
        // 负责人
        var responsible = sourceDoc.getItemValueAsString("负责人") || sourceDoc.getItemValueAsString("经办人");
        if (responsible) {
            reminderDoc.addStringItem("负责人", responsible);
        }
        
        // 提醒状态
        reminderDoc.addStringItem("提醒状态", "待提醒");
        
        // 是否已提醒
        reminderDoc.addStringItem("是否已提醒", "否");
        
        debug("提醒记录字段设置完成");

    } catch (e) {
        debug("设置提醒字段时发生错误: " + e.message);
        throw e;
    }
}

/**
 * 启动提醒流程（可选）
 * @param {Object} docProcess - 文档处理对象
 * @param {Object} reminderDoc - 提醒记录文档
 * @param {string} billNo - 单据编号
 * @param {Object} user - 用户对象
 */
function startReminderFlow(docProcess, reminderDoc, billNo, user) {
    try {
        // 重新获取提醒记录对象
        var doc = LoadByIndexFieldValue("tlk_psm_01_personal_affairs_management_list", "ITEM_编号", billNo, true);

        if (doc) {
            // 这里可以设置具体的流程ID，如果有的话
            var flowid = "__PSM_REMINDER_FLOW_ID"; // 请替换为实际的流程ID
            var billParams = createParamsTable();
            billParams.setParameter("_flowid", flowid);

            // 启动流程
            docProcess.doStartFlowOrUpdate(doc, billParams, user);
            debug("成功启动提醒流程: " + billNo);
        }

    } catch (e) {
        debug("启动提醒流程失败: " + e.message);
        // 流程启动失败不影响提醒记录的创建
    }
}

/**
 * 获取当前正在编辑的文档
 * 这个函数需要根据实际的系统API来实现
 * @returns {Object} 当前文档对象
 */
function getCurrentDocument() {
    // 这里需要根据您的系统实际情况来实现
    // 可能的实现方式：
    // 1. 从全局变量获取
    // 2. 从请求参数获取
    // 3. 从会话中获取

    // 示例实现（需要根据实际情况调整）:
    try {
        // 方式1: 从全局变量获取（如果系统提供）
        if (typeof currentEditingDoc !== 'undefined') {
            return currentEditingDoc;
        }

        // 方式2: 从请求参数获取文档ID
        var docId = getParameter("docId") || getParameter("id");
        if (docId) {
            return findDocument(docId);
        }

        // 方式3: 其他获取方式...
        return null;

    } catch (e) {
        debug("获取当前文档失败: " + e.message);
        return null;
    }
}

/**
 * 手动触发创建提醒记录的函数
 * 可以在表单保存事件中调用此函数
 * @param {Object} doc - 个人事务管理文档对象
 */
function triggerCreateReminder(doc) {
    try {
        var user = GetAssistant();
        var applicationId = getApplication();
        var docProcess = getDocProcess(applicationId);

        // 检查是否是个人事务管理表
        if (doc.getFormname() !== "tlk_psm_01_personal_affairs_management") {
            debug("文档不是个人事务管理表，跳过自动提醒创建");
            return;
        }

        // 检查是否已经存在提醒记录（避免重复创建）
        var existingReminder = checkExistingReminder(doc.getId());
        if (existingReminder) {
            debug("已存在提醒记录，跳过创建");
            return;
        }

        // 创建事务提醒记录
        createReminderRecord(docProcess, user, doc, applicationId);

    } catch (e) {
        debug("手动触发创建提醒记录失败: " + e.message);
        Packages.java.lang.System.out.println("Error in triggerCreateReminder: " + e);
    }
}

/**
 * 检查是否已存在提醒记录
 * @param {string} sourceDocId - 源文档ID
 * @returns {boolean} 是否存在
 */
function checkExistingReminder(sourceDocId) {
    try {
        var sql = "SELECT COUNT(*) as count FROM tlk_psm_01_personal_affairs_management_list WHERE ITEM_关联事务ID = '" + sourceDocId + "'";
        var data = MagicFindBySql("magic", sql);
        if (data && data.get("count") > 0) {
            return true;
        }
        return false;
    } catch (e) {
        debug("检查已存在提醒记录失败: " + e.message);
        return false;
    }
}
