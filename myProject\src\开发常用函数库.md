# 常用函数库（汇总）

本文档汇总项目中常用的 JS 函数库，按模块分类列出函数名、功能描述、主要参数和返回值，以及注意事项，便于查阅和维护。

**使用说明：**

- 所有函数均为 JavaScript 语言编写，适用于 OA 系统环境

- 函数调用前请确保相关数据库连接正常

- 涉及用户权限的操作需要先验证当前用户身份

- 建议在测试环境验证后再在生产环境使用

- **版本：** 初版

- **更新：** 2025-09-09

- **维护：** 开发团队

## 目录

- **SysUserUtils** - 用户账号、角色、权限管理相关函数
- **SupplyChainUtils** - 采购、库存、付款等供应链管理函数
- **SalesUtils** - 销售订单、客户管理相关函数
- **FlowUtils** - 工作流程相关工具函数
- **SystemUtils** - 系统通用工具函数
- **DBUtils** - 数据库操作相关函数
- **ChangeMoney** - 金额转换工具函数
- **PDIUtils** - 商品数据导入工具函数
- **FinancialUtils** - 财务相关工具函数
- **ITUtils** - IT系统维护相关函数

## 快速查找

**常用功能索引：**

- 用户信息查询：`getUserIdByUserName()`, `getUserVoByUserName()`
- 权限验证：`IsRole()`, `HaveRole()`
- 商品信息获取：`GetGoodsMsgFromCGPlan()`, `GetTCGMsg()`
- 流程操作：`SubmitFlow()`, `InterveneFlow()`, `BackFlow()`
- 采购相关：`CheckCGPaymentPrice()`, `SplitCGPurchaseBill()`

---

## 1. SysUserUtils

**模块说明：** 与用户、角色、平台管理员、代理人等相关的工具函数。主要用于用户信息查询、角色权限验证、平台管理员获取等操作。

**数据来源：** 主要操作 obpm5 数据库的 t_user、t_flow_proxy 等表。

**使用场景：**

- 流程审批中的用户身份验证
- 权限控制和角色判断
- 用户信息查询和管理
- 代理人设置和查询

**注意事项：**

- 所有用户查询都基于域ID 'ZbcBRS8hXO7lfuoz6mP'
- 离职用户会自动查找代理人
- 角色验证支持多角色逗号分隔

**主要函数：**

```javascript
/**
 * 通过用户姓名获取用户ID
 * @param {string} UserName - 用户姓名
 * @returns {string} 用户ID,未找到返回空字符串
 */
function getUserIdByUserName(UserName) {
  var sql = "select ID from t_user tu where DOMAINID = 'ZbcBRS8hXO7lfuoz6mP' AND NAME = '" + UserName + "'";
  var data = MagicFindBySql("obpm5", sql);
  if (data) {
    return data.get("ID");
  } else {
    return "";
  }
}

/**
 * 通过用户姓名获取登录账号
 * @param {string} UserName - 用户姓名
 * @returns {string} 用户登录账号,未找到返回空字符串
 */
function getUserLoginnoByUserName(UserName) {
  var sql = "select ID from t_user tu where DOMAINID = 'ZbcBRS8hXO7lfuoz6mP' AND NAME = '" + UserName + "'";
  var data = MagicFindBySql("obpm5", sql);
  if (data) {
    var userVO = getUserById(data.get("ID"));
    if (userVO) {
      return userVO.getLoginno();
    } else {
      return "";
    }
  } else {
    return "";
  }
}

/**
 * 通过用户姓名获取用户对象
 * @param {string} UserName - 用户姓名
 * @returns {Object} 用户对象,未找到返回空字符串
 */
function getUserVoByUserName(UserName) {
  var sql = "select ID from t_user tu where DOMAINID = 'ZbcBRS8hXO7lfuoz6mP' AND NAME = '" + UserName + "'";
  var data = MagicFindBySql("obpm5", sql);
  if (data) {
    var userVO = getUserById(data.get("ID"));
    if (userVO) {
      return userVO
    } else {
      return "";
    }
  } else {
    return "";
  }
}

/**
 * 检查当前用户是否拥有指定角色
 * @param {string} RoleNames - 角色名称,多个用逗号分隔
 * @returns {boolean} true表示用户拥有其中任一角色
 */             
function IsRole(RoleNames) {
  var roleNames = splitString(RoleNames, ",");
  for (var i = 0; i < roleNames.length; i++) {
    var roleName = roleNames[i];
    var roles = getWebUser().getRoles();
    for (var it = roles.iterator(); roles != null && it.hasNext();) {
      var role = it.next();
      if (roleName.equals(role.getName())) {
        return true;
      }
    }
  }
  return false;
}

/**
 * 检查指定用户是否拥有指定角色
 * @param {Object} user - 用户对象
 * @param {string} roleNames - 角色名称,多个用逗号分隔
 * @returns {boolean} true表示用户拥有其中任一角色
 */
function HaveRole(user, roleNames){
  if (isNotNull(user) == false || isNotNull(roleNames) == false)
    return false;
  var roleNames = splitString(roleNames, ",");
  for (var i = 0; i < roleNames.length; i++) {
    var roleName = roleNames[i];
    var roles = user.getRoles();
    for (var it = roles.iterator(); roles != null && it.hasNext();) {
      var role = it.next();
      if (roleName.equals(role.getName())) {
        return true;
      }
    }
  }
  return false;
}

/**
 * 获取智能助手用户对象
 * @returns {Object} 智能助手用户对象
 */
function GetAssistant() {
  var userId = getUserIdByUserName("智能助手");
  return getUserById(userId);
}

/**
 * 平台名称转换
 * @param {string} platform - 原平台名称
 * @returns {string} 转换后的平台名称
 */
function TranslatePlatform(platform) {
  if ("淘宝-1688".equals(platform))
    return "1688";
  else
    return platform;
}
/**
 * 根据运营平台获取其管理员
 * 按优先级查找：平台+账号+区域 > 平台+空账号 > 仅平台
 * @param {string} platform - 运营平台名称
 * @param {string} account - 账号
 * @param {string} area - 区域
 * @returns {Object} 管理员用户对象,未找到返回null
 */
function GetPlatformManager(platform, account, area) {
  var sql = "SELECT * FROM tlk_ywim_01_platform_manager WHERE ITEM_运营平台 = '" + platform + "' and ITEM_账号 = '" + account + "' AND ITEM_区域 = '" + area + "'";
  var data = MagicFindBySql("magic", sql);
  if (data == null) {
    sql = "SELECT * FROM tlk_ywim_01_platform_manager WHERE ITEM_运营平台 = '" + platform + "' and (ITEM_账号 IS NULL OR ITEM_账号 = '')";
    debug("GetPlatformManager.sql=" + sql);
    data = MagicFindBySql("magic", sql);
  }
  if (data == null) {
    sql = "SELECT * FROM tlk_ywim_01_platform_manager WHERE ITEM_运营平台 = '" + platform + "'";
    data = MagicFindBySql("magic", sql);
  }
  if (data != null) {
    var userId = data.get("ITEM_管理员");
    return getUserById(userId);
  } else
    return null;
}

/**
 * 通过用户ID获取用户姓名
 * @param {string} userId - 用户ID
 * @returns {string} 用户姓名
 */
function getUserNameById(userId) {
  var user = getUserById(userId)
  var userName = user.getName()
  return userName
}

/**
 * 获取在职用户，如果用户离职则查找代理人
 * 递归查找代理人，最多查找5层，代理必须为所有流程
 * @param {string} userName - 用户姓名
 * @param {number} level - 递归层级（内部使用）
 * @returns {Object} 在职用户对象,未找到返回null
 */
function GetWorkingUser(userName, level) {
  level += 1;
  debug("GetWorkingUser=" + userName + ", level=" + level);
  if (isNotNull(userName) == false) {
    debug("userName is null");
    return null;
  }
  var userSql = "SELECT * FROM obpm5.t_user WHERE NAME = '" + userName + "'";
  debug("userSql=" + userSql);
  var user = MagicFindBySql("magic", userSql);
  if (isNotNull(user) == false) {
    return null;
  }
  var userId = user.get("ID");
  if (user.get("STATUS") == 1)
    return getUserById(userId);
  if (level > 5)
    return null;
  var sql = "select distinct tf.OWNER, tf.AGENTSNAME from magic5.t_flow_proxy tf " +
    "left join obpm5.t_user tu on tf.OWNER = tu.ID " +
    "where tf.ENDPROXYTIME >= Now() AND OWNER = '" + userId + "'";
  debug("DBUtils.GetAgents.sql = " + sql);
  var data = MagicFindBySql("magic", sql);
  var names = splitString(data.get("AGENTSNAME"), ";");
  for (var i = 0; i < names.length; i++) {
    var name = names[i];
    var temp = GetWorkingUser(name, level);
    if (temp != null)
      return temp;
  }
}
```

---

## 2. SupplyChainUtils

**模块说明：** 采购、普源/普源系统、库存、组装、付款等供应链相关的函数集合。

**主要功能：**

- 采购申请和订单处理
- SKU信息获取和更新
- 库存查询和计算
- 付款申请处理
- 供应商管理
- 组装任务管理
- 价格变动检查

**数据来源：**

- DATACENTER_BUSINESS: PowerBI业务数据
- DATACENTER_SHOPELF: 商品库存数据
- magic: 系统业务数据

**使用场景：**

- 采购流程自动化处理
- 商品信息查询和维护
- 库存状态监控
- 价格变动分析
- 组装任务管理

**注意事项：**

- 涉及金额计算时注意精度问题
- 普源系统数据同步可能有延迟
- 组装任务需要检查物料库存充足性
- 价格变动超过阈值需要填写原因

**主要函数：**

```javascript
/**
 * 从PowerBI的采购前置系统获取商品运营信息
 * 包含:SKU描述、供应商、采购周期、库存等信息
 * @param {string} SKU - 商品SKU编码
 * @returns {ArrayList} 商品信息列表
 */
function GetGoodsMsgFromCGPlan(SKU) {
  var sql = "SELECT tcprl.*, bs.SupplierName FROM BUSINESS.T_CG_PROCUREMENT_REQUIREMENTS_LATEST tcprl " +
    "LEFT JOIN SHOPELF.B_Goods bg on tcprl.SKU = bg.SKU " +
    "LEFT JOIN SHOPELF.B_Supplier bs on bg.SupplierID = bs.NID " +
    "WHERE tcprl.SKU = '" + SKU + "'";
  var query = queryByDSName("DATACENTER_BUSINESS", sql);
  var array = createObject("java.util.ArrayList");
  if (query != null) {
    for (var iterator = query.iterator(); iterator.hasNext();) {  //获取商品信息
      var goods = iterator.next();
      array.add(goods);
    }
  }
  return array;
}

/**
 * 获取单个SKU的采购计划信息
 * @param {string} SKU - 商品SKU编码
 * @returns {Object} 商品信息对象,未找到返回null
 */
function GetTCGMsg(SKU) {
  var arr = GetGoodsMsgFromCGPlan(SKU);
  if (arr != null && arr.size() > 0) {
    return arr.get(0);
  }
  return null;
}

/**
 * 检查采购付款申请单价格变动情况
 * 对比当前价格与最近一次采购价格，超过阈值需要说明原因
 * @param {string} PaymentID - 付款申请单ID
 * @returns {string} 价格变动提示信息,无变动返回空字符串
 */
function CheckCGPaymentPrice(PaymentID) {
  var payment = findDocument(PaymentID);
  if (payment == null)
    return "";
  var orderBill = payment.getItemValueAsString("采购订单编号");
  var puyuanBill = payment.getItemValueAsString("普源采购订单编号");
  if (isNotNull(puyuanBill))
    return AnalysePuYuanCGBillPrice(puyuanBill);
  else
    return AnalyseOrderBillPrice(orderBill);
}

/**
 * 分析普源采购订单价格变动情况

 * @param {string} puyuanBill - 普源采购订单编号
 * @returns {string} 价格变动提示信息
 */
function AnalysePuYuanCGBillPrice(puyuanBill) {
  if (isNotNull(puyuanBill) == false)
    return "普源订单编号不能为空。";
  var pyOrder = GetCGOrder(puyuanBill);
  if (!pyOrder)
    return puyuanBill + "普源订单不存在，可能是普源订单编号录入借误或普源订单还没有同步。";
  var makeDate = pyOrder.get("makedate");
  makeDate = adjustDay(makeDate, -1);
  var makeDateStr = format(makeDate, "yyyy-MM-dd");
  var sql = "SELECT bg.SKU, cs2.TaxPrice from CG_STOCKORDERM cs LEFT JOIN CG_STOCKORDERD cs2 on cs.NID = cs2.StockOrderNID " +
    "LEFT JOIN B_GOODSSKU bg ON cs2.GoodsSKUID = bg.NID " +
    "WHERE cs.BillNumber = '" + puyuanBill + "'";
  var result = "";
  var arr = MagicQueryBySql("DATACENTER_SHOPELF", sql);
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var vo = arr.get(j);
      var sku = vo.get("SKU");
      var currentPrice = vo.get("TaxPrice");
      var lastPrice = GetRecentPriceBySKU(sku, makeDateStr);
      var value = 0;
      if (lastPrice > 0)
        value = (currentPrice - lastPrice) / lastPrice;
      value = Packages.java.lang.Math.abs(value);
      if (currentPrice.equals(lastPrice))
        continue;
      debug("currentPrice1=" + currentPrice);
      debug("lastPrice1=" + lastPrice);
      if (isNotNull(result))
        result += ",";
      result += sku;
    }
  }
  if (isNotNull(result))
    result = "发现本次采购价格与最近一次采购价格不一致，请在【单价变动原因】里对以下SKU说明价格变动原因：" + result;
  return result;
}

/**
 * 分析采购订单价格变动情况
 * @param {string} orderBill - 采购订单编号
 * @returns {string} 价格变动提示信息
 */
function AnalyseOrderBillPrice(orderBill) {
  if (isNotNull(orderBill) == false)
    return "采购订单编号不能为空";
  var pyOrder = LoadByIndexFieldValue("tlk_gy_cg_01_order_main", "ITEM_编号", orderBill, true);
  var makeDateStr = pyOrder.getItemValueAsString("申请日期"); //AsString
  var makeDate = parseDate(makeDateStr, "yyyy-MM-dd");
  makeDate = adjustDay(makeDate, -1);
  makeDateStr = format(makeDate, "yyyy-MM-dd");
  var domainid = getDomainid();
  var sql = "select '" + domainid + "' as DOMAINID, tgcod.ITEM_SKU, tgcod.ITEM_含税单价 from tlk_gy_cg_01_order_main tgcom " +
    "left join tlk_gy_cg_01_order_detail tgcod on tgcom.ID = tgcod.PARENT " +
    "where tgcom.ITEM_编号 = '" + orderBill + "'";
  var result = "";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var vo = iter.next();
      var sku = vo.getItemValueAsString("SKU");
      var currentPrice = vo.getItemValueAsDouble("含税单价");
      var lastPrice = GetRecentPriceBySKU(sku, makeDateStr);
      var value = (currentPrice - lastPrice) / lastPrice;
      value = Packages.java.lang.Math.abs(value);
      //if (value < 0.03)
      if (lastPrice == currentPrice)
        continue;
      if (isNotNull(result))
        result += ",";
      result += sku;
    }
  }
  if (isNotNull(result))
    result = "发现本次采购价格和最近一次采购价格变动超过3%，请在【单价变动原因】里对以下SKU说明价格变动原因：" + result;
  return result;
}

/**
 * 通过普源订单号查找对应的采购付款单编号
 * @param {string} StockOrder_BillNumber - 普源采购订单编号
 * @returns {string} 采购付款单编号,未找到返回空字符串
 */
function GetCGPayBillNumber(StockOrder_BillNumber) {
  if (isNotNull(StockOrder_BillNumber) == false)
    return;
  var sql = "SELECT * FROM tlk_gy_cg_02_payment tgcp where ITEM_普源采购订单编号 = '" + StockOrder_BillNumber + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    var iter = query.iterator();
    if (iter != null && iter.hasNext()) {
      doc = iter.next();
      return doc.getItemValueAsString("编号");
    }
  }
  return "";
}

/**
 * 获取普源订单中的商品信息汇总
 * 返回格式：SKU:单价*数量,SKU:单价*数量...
 * @param {string} StockOrder_BillNumber - 普源采购订单编号
 * @returns {string} 商品信息字符串
 */
function GetCGBillGoodsMsg(StockOrder_BillNumber) {
  if (isNotNull(StockOrder_BillNumber) == false)
    return;
  var sql = "select CONCAT(bg.SKU, ':', cs2.TaxPrice, '*', cs2.Amount) as Field1 from SHOPELF.CG_STOCKORDERM cs " +
    "left join SHOPELF.CG_STOCKORDERD cs2 on cs.NID = cs2.StockOrderNID " +
    "left join B_Goods bg on cs2.GoodsID = bg.NID " +
    "where cs.BillNumber = '" + StockOrder_BillNumber + "'";
  var result = "";
  var arr = MagicQueryBySql("DATACENTER_SHOPELF", sql);
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var vo = arr.get(j);
      if (isNotNull(result))
        result += ",";
      result += vo.get("field1");
    }
  }
  return result;
}

/**
 * 校验采购申请单的商品明细供应商信息是否完整
 * @param {string} billId - 采购申请单ID
 * @returns {string} 缺少供应商信息的SKU列表提示
 */
function ValidPurchaseSupplier(billId) {
  var sql = "SELECT * FROM tlk_gy_cg_01_purchase_detail where PARENT = '" + billId + "'";
  var query = queryBySQL(sql);
  var result = "";
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      var sku = data.getItemValueAsString("SKU");
      var supplier = data.getItemValueAsDouble("供应商");
      if (isNotNull(supplier) == false) {
        if (isNotNull(result))
          result += ",";
        result += sku;
      }
    }
  }
  if (isNotNull(result))
    return "以下SKU缺少供应商信息，请在普源系统的商品信息里设置供应商，等待5分钟后，点击【补充商品明细供应商】按钮。";
}

/**
 * 为采购申请单的商品明细补充供应商信息
 * @param {string} billId - 采购申请单ID
 */
function FixPurchaseSupplier(billId) {
  var process = getDocProcess(getApplication());
  var subfdocs = LoadChildDocs("tlk_gy_cg_01_purchase_detail", billId);   //获取子流程文档列表
  if (subfdocs != null) {
    for (var it = subfdocs.iterator(); it.hasNext();) {  //遍历文档
      var doc = it.next();
      var skus = createObject("java.util.ArrayList");
      skus.add(doc.getItemValueAsString("SKU"));
      var goods = GetGoodsBySKUs(skus);
      if (goods != null && goods.size() > 0) {
        doc.findItem("供应商").setValue(goods.get(0).get("SupplierName"));
        doc.findItem("采购员").setValue(goods.get(0).get("Purchaser"));
        FillPuchaseDetail(doc);
        process.doUpdate(doc);
      }
    }
  }
}

/**
 * 校验采购付款申请单中是否登记价格变动原因
 * @param {Object} paymentBill - 采购付款申请单对象
 * @returns {string} 未填写价格变动原因的SKU提示信息
 */
function CheckPaymentPriceChangeFillReason(paymentBill) {
  if (isNotNull(paymentBill.getId()) == false)
    return;
  var sql = "SELECT * FROM tlk_gy_cg_02_payment_price_change WHERE PARENT = '" + paymentBill.getId() + "'";
  var query = queryBySQL(sql);
  var result = "";
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      var sku = data.getItemValueAsString("SKU");
      var reason = data.getItemValueAsString("价格变动原因");
      if (isNotNull(reason))
        continue;
      if (isNotNull(result))
        result += ",";
      result += sku;
    }
  }
  if (isNotNull(result))
    result += "的价格发生变动，请填写相应价格变动原因。";
  return result;
}

/**
 * 从采购订单的商品明细中选择商品对象，添加前需要和当前SKU的供应商对比
 * 如果供应商不一致，不允许添加，并添加合并备注
 * @param {string} selectedId - 选中的商品明细ID
 */
function CGOrderPickGoodsToChart(selectedId) {
  if (isNotNull(selectedId) == false)
    return;
  //检查供应商是不是一致
  if (CheckGoodsSupplierIsSame(selectedId) == false)
    return;
  var request = $WEB.getParamsTable().getHttpRequest();
  var cache = request.getSession().getAttribute("采购订单商品明细迁移缓存");
  if (isNotNull(cache) == false)
    cache += ";";
  cache += selectedId;
  request.getSession().setAttribute("采购订单商品明细迁移缓存", cache);  //
}

/**
 * 检测选择的采购订单商品明细和缓存里的商品明细是否为同一个供应商
 * @param {string} selectedId - 选中的商品明细ID
 * @returns {string} 供应商名称
 */
function GetGoodsSupplierName(selectedId) {
  if (isNotNull(selectedId) == false)
    return true;
  var request = $WEB.getParamsTable().getHttpRequest();
  var sel = request.getSession().getAttribute("采购订单商品明细迁移缓存");
  sel = sel.replace(";", "','");
  var domainid = getDomainid();
  var sql = "SELECT DISTINCT tgcom.domainid, tgcom.ITEM_供应商名称 FROM tlk_gy_cg_01_order_main tgcom " +
    "LEFT JOIN tlk_gy_cg_01_order_detail tgcod on tgcom.ID = tgcod.PARENT " +
    "WHERE tgcod.ID in ('" + sel + "')";
  var query = queryBySQL(sql);
  var result = "";
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      if (isNotNull(result))
        result += ",";
      result = data.getItemValueAsString("供应商名称");
    }
  }
  return result;
}

/**
 * 获取商品对应的采购订单编号
 * @param {string} selectedId - 选中的商品明细ID
 * @returns {string} 采购订单编号
 */
function GetGoodsBillNumber(selectedId) {
  if (isNotNull(selectedId) == false)
    return true;
  var request = $WEB.getParamsTable().getHttpRequest();
  var sel = request.getSession().getAttribute("采购订单商品明细迁移缓存");
  sel = sel.replace(";", "','");
  var domainid = getDomainid();
  var sql = "SELECT DISTINCT tgcom.domainid, tgcom.ITEM_编号 FROM tlk_gy_cg_01_order_main tgcom " +
    "LEFT JOIN tlk_gy_cg_01_order_detail tgcod on tgcom.ID = tgcod.PARENT " +
    "WHERE tgcod.ID in ('" + sel + "')";
  var query = queryBySQL(sql);
  var result = "";
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      if (isNotNull(result))
        result += ",";
      result = data.getItemValueAsString("编号");
    }
  }
  return result;
}

/**
 * 把缓存里的商品明细移动到现有的订单里
 * @param {string} orderBillID - 采购订单ID
 * @param {string} selectId - 选中的商品明细ID列表
 */
function CGOrderGoodsChartToBill(orderBillID, selectId) {
  var doc = findDocument(orderBillID);
  var supplierName = doc.getItemValueAsString("供应商名称");
  var process = getDocProcess(getApplication());
  var sel = selectId;
  sel = sel.replace(";", "','");
  //只写入
  var sql = "SELECT tgcod.* FROM tlk_gy_cg_01_order_main tgcom " +
    "LEFT JOIN tlk_gy_cg_01_order_detail tgcod on tgcom.ID = tgcod.PARENT " +
    "WHERE tgcod.ID in ('" + sel + "') AND ITEM_供应商名称 = '" + supplierName + "'";
  println("CGOrderGoodsChartToBill=" + sql);
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var detailDoc = iter.next();
      var sourceBill = detailDoc.getParent();
      var sourceNo = sourceBill.getItemValueAsString("迁移原单号");
      if (isNotNull(sourceNo))
        sourceNo += ",";
      sourceNo += sourceBill.getItemValueAsString("编号");
      detailDoc.findItem("迁移原单号").setValue(sourceNo);
      detailDoc.setParent(doc.getId());
      process.doUpdate(detailDoc);
    }
  }
}

/**
 * 把缓存里的商品明细创建为新的采购订单
 * @param {string} selectId - 选中的商品明细ID列表
 * @returns {Object} 新创建的采购订单对象
 */
function CGOrderGoodsChartToNewBill(selectId) {
  var user = getWebUser();
  var sel = selectId.replace(";", "','");
  var sql = "SELECT DISTINCT tgcom.* FROM tlk_gy_cg_01_order_main tgcom " +
    "LEFT JOIN tlk_gy_cg_01_order_detail tgcod on tgcom.ID = tgcod.PARENT " +
    "WHERE tgcod.ID in ('" + sel + "')";
  var query = queryBySQL(sql);
  var sourceDoc = null;
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      sourceDoc = iter.next();
      break;
    }
  }
  if (sourceDoc == null)
    return;
  var applicationId = getApplication();
  var docProcess = getDocProcess(applicationId);
  //创建采购订单                              
  var cgdPrefix = "GYOM-";
  var cgBillNo = countNext2(cgdPrefix, true, true, true, 4);
  var billFormName = "GY_CG_01_Order_Main";
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
  var Form = formProcess.doViewByFormName(billFormName, applicationId);
  var params = createParamsTable();
  orderMainDoc = docProcess.doNew(Form, user, params);
  orderMainDoc.addStringItem("编号", cgBillNo);
  orderMainDoc.addStringItem("采购申请单号", sourceDoc.getItemValueAsString("采购申请单号"));
  orderMainDoc.addStringItem("采购类型", sourceDoc.getItemValueAsString("采购类型"));
  orderMainDoc.addDateItem("申请日期", sourceDoc.getItemValueAsDate(""));
  orderMainDoc.addStringItem("制单人", user.getName());
  orderMainDoc.addStringItem("采购员", user.getName());
  orderMainDoc.addStringItem("申请备注", sourceDoc.getItemValueAsString("申请备注"));
  orderMainDoc.addStringItem("合同号", sourceDoc.getItemValueAsString("合同号"));
  orderMainDoc.addStringItem("供应商名称", sourceDoc.getItemValueAsString("供应商名称"));
  orderMainDoc.addStringItem("原供应商名称", sourceDoc.getItemValueAsString("原供应商名称"));
  orderMainDoc.addStringItem("支付对象", sourceDoc.getItemValueAsString("支付对象"));
  orderMainDoc.addStringItem("开户银行", sourceDoc.getItemValueAsString("开户银行"));
  orderMainDoc.addStringItem("银行账号", sourceDoc.getItemValueAsString("银行账号"));
  docProcess.doCreate(orderMainDoc);//创建

  //重新获取采购订单对象                              
  orderMainDoc = LoadByIndexFieldValue("tlk_gy_cg_01_order_main", "ITEM_编号", cgBillNo, true); //这个方法是自定义函数，用于获取某个表的对象，使用正常的。                                
  var flowid = "__BSBln5AAXQ2vaDSVfAK";//获取流程id
  var billParams = createParamsTable();
  billParams.setParameter("_flowid", flowid);
  docProcess.doStartFlowOrUpdate(orderMainDoc, billParams, user); //启动流程                               
  CGOrderGoodsChartToBill(orderMainDoc.getId(), selectId);
  return orderMainDoc;
}

/**
 * 在采购申请单中录入SKU，获取商品其他信息
 * @param {Object} doc - 采购申请单明细文档对象
 * @returns {Object} 填充后的文档对象
 */
function FillPuchaseDetail(doc) {
  var sku = doc.getItemValueAsString("SKU");
  var arr = GetGoodsMsgFromCGPlan(sku);
  if (arr == null || arr.size() < 1) {
    debug("no data.");
    return doc;
  }
  var vo = arr.get(0);
  doc.findItem("商品名称").setValue(vo.get("SKUDescript"));
  doc.findItem("供应商").setValue(vo.get("SupplierName"));
  var storeName = GetStoreBySKU(sku);
  doc.findItem("采购仓库").setValue(storeName);
  doc.findItem("采购员").setValue(vo.get("Purchaser"));
  doc.findItem("七天销量").setValue(vo.get("SaleAmount7D"));
  doc.findItem("三十天销量").setValue(vo.get("SaleAmount30D"));
  doc.findItem("ALI1688三十天销量").setValue(vo.get("SaleAmountALI30D"));
  doc.findItem("速卖通三十天销量").setValue(vo.get("SaleAmountSMT30D"));
  doc.findItem("是否拆分采购").setValue(vo.get("IsSplitBuy"));
  doc.findItem("商品等级").setValue(vo.get("productgrade"));
  doc.findItem("历史平均每单订购数").setValue(vo.get("HisAvgQtyPerOrder"));
  doc.findItem("商品等级").setValue(vo.get("ProductGrade"));
  doc.findItem("当前本地库存").setValue(vo.get("CurrentLocalInventory"));
  doc.findItem("广州仓库存").setValue(vo.get("CurrentGZInventory"));
  doc.findItem("北海仓库存").setValue(vo.get("CurrentBHInventory"));
  doc.findItem("河北仓库存").setValue(vo.get("CurrentHBInventory"));
  doc.findItem("当前FBA库存").setValue(vo.get("CurrentFBAInventory"));
  doc.findItem("当前FBA库存售完月").setValue(vo.get("CurrentFBAInventorySOM"));
  doc.findItem("货期").setValue(vo.get("procurementcycle"));
  doc.findItem("采购在途").setValue(vo.get("stocknotinamount"));
  doc.findItem("FBA在途").setValue(vo.get("fbashippedamount"));
  doc.findItem("现有库存可组合数").setValue(vo.get("GroupAmount"));
  doc.findItem("停售").setValue(vo.get("USED"));
  var totalInventory = CalTotalInventory(vo, sku);
  var field = doc.findItem("当前总库存");
  if (isNotNull(field))
    field.setValue(totalInventory);
  //OA采购在途数
  var oaNotInStock = GetOANotInStock(sku);
  if (doc.findItem("OA采购在途数量") != null)
    doc.findItem("OA采购在途数量").setValue(oaNotInStock);
  //待组装数
  var asscembleAmount = GetAssemblyAmount(sku);
  if (doc.findItem("待组装数量") != null)
    doc.findItem("待组装数量").setValue(asscembleAmount);
  return doc;
}

/**
 * 计算商品总库存
 * @param {Object} vo - 商品信息对象
 * @param {string} sku - 商品SKU
 * @returns {number} 总库存数量
 */
function CalTotalInventory(vo, sku) {
  //OA备货: STA发货主线备货需求 - 拣出数量
  //当前总库存：OA备货+海外仓/FBA在途+采购为日入库+广州可用+海外仓/FBA可用
  //OA备货
  var PickAmount = GetSTANeedAmount(sku);
  //海外仓/FBA在途：
  var fbaShipping = parseDouble(vo.get("FBAShippedAmount"));
  var stockNotInAmount = parseDouble(vo.get("stocknotinamount"));
  var currentGZInventory = parseDouble(vo.get("CurrentGZInventory"));
  var currentFBAInventory = parseDouble(vo.get("CurrentFBAInventory"));
  var result = fbaShipping + stockNotInAmount + currentGZInventory + currentFBAInventory;//PickAmount;
  return result;
}

/**
 * 获取STA备货所需的数量
 * @param {string} sku - 商品SKU
 * @returns {number} STA备货需求数量
 */
function GetSTANeedAmount(sku) {
  //STA备货所需的数量
  var sql = ReadSql("STAUtils_UnPickAmountBySKU");
  sql = ReplaceAll(sql, "SKU", sku);
  var data = MagicFindBySql("magic", sql);
  if (isNotNull(data))
    return parseDouble(data.get("ITEM_拣出数量"));
  else
    return 0;
}

//把付申明细清单的记录的付款设置成当前日期
function SetPaymentSKUPayDate(parentDocument) {
  var dataStr = format(getToday(), "yyyy-MM-dd");
  var sql = "Update tlk_gy_cg_02_payment_sku set ITEM_付款日期 = '" + dataStr + "' WHERE PARENT = '" +
    parentDocument.getId() + "' AND ITEM_付款日期 IS NULL";
  updateByDSName("magic", sql);
}

function SendQQMessageForDisableCGApply(applyBillNo, msg) {
  var doc = LoadByIndexFieldValue("tlk_gy_cg_01_purchase_main", "ITEM_编号", applyBillNo, true);
  var content = applyBillNo + "采购申请单没有通过审批";
  if (isNotNull(msg))
    content += msg;
  SendQQMessageByDoc(doc, "采购申请单", "通知", content);
}

function SendQQMessageForDisableCGOrder(doc) {
  var cgApplyBillNo = doc.getItemValueAsString("采购申请单号");
  var content = "原因：作废" + doc.getItemValueAsString("编号") + "采购订单，经办人：" + doc.getItemValueAsString("制单人");
  SendQQMessageForDisableCGApply(cgApplyBillNo, content);
}

function SendQQMessageForDisableCGPayment(doc) {
  var cgApplyBillNo = doc.getItemValueAsString("采购申请单");
  if (isNotNull(cgApplyBillNo) == false)
    return; //没有采购申请单的不发送通知
  var content = "原因：作废" + cgApplyBillNo + "采购付款申请单，经办人：" + doc.getItemValueAsString("申请人");
  SendQQMessageForDisableCGApply(cgApplyBillNo, content);
}

/**
 * 拆分采购申请单
 * 根据商品采购员将一张采购单拆分成多张
 * 业务流程:
 * 1. 获取单据中所有SKU的采购员
 * 2. 按采购员分组创建新单据
 * 3. 移动对应商品到新单据
 * @param {Document} doc - 采购申请单文档
 * @param {User} user - 当前用户
 */
function SplitCGPurchaseBill(doc, user) {
  var arr = LoadChildDocs("tlk_gy_cg_01_purchase_detail", doc.getId());
  var cgers = createObject("java.util.ArrayList");
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var vo = arr.get(j);
      var billCgerName = vo.getItemValueAsString("采购员");
      if (isCger(billCgerName) == false)
        billCgerName = CG_DefaultCgerName();
      cgers = addCger(cgers, billCgerName);
    }
  }
  var applicationId = getApplication();
  var docProcess = getDocProcess(applicationId);
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
  splitBill(docProcess, formProcess, doc, arr, cgers, user);
}

function addCger(cgers, cger) {
  //采购申请单明细商品内的采购员名称去重
  if (cgers.size() < 1)
    cgers.add(cger);
  else {
    var same = false;
    for (var j = 0; j < cgers.size(); j++) {
      var vo = cgers.get(j);
      if (vo.equals(cger)) {
        same = true;
        break;
      }
    }
    if (same == false)
      cgers.add(cger);
  }
  return cgers;
}

function splitBill(docProcess, formProcess, doc, arr, cgers, user) {
  //拆分采购申请单
  var stayCgerName = StayCgerName(cgers, user);
  for (var j = 0; j < cgers.size(); j++) {
    var cgerName = cgers.get(j);
    if (cgerName.equals(stayCgerName))
      continue;
    var childVos = createObject("java.util.ArrayList");
    for (var i = 0; i < arr.size(); i++) {
      var childVo = arr.get(i);
      var skuCgerName = childVo.getItemValueAsString("采购员");
      if (isCger(skuCgerName) == false)
        skuCgerName = CG_DefaultCgerName();
      if (cgerName.equals(skuCgerName))
        childVos.add(childVo);
    }
    if (childVos.size() > 0) {
      //创建采购申请单
      MakeCGPurchaseBill(docProcess, formProcess, doc, childVos, cgerName);
    }
  }
}

function StayCgerName(cgers, user) {
  //判断当前用户是否在明细内存在SKU的采购员，如果是则不迁移该用户的记录
  //如果不存在则返回首个SKU的采购员
  println("StayCgerName.cgers=" + cgers);
  println("StayCgerName.user=" + user);
  var same = false;
  if (cgers.size() < 1)
    return CG_DefaultCgerName();
  for (var j = 0; j < cgers.size(); j++) {
    var vo = cgers.get(j);
    if (vo.equals(user.getName()) == false) {
      same = true;
      break;
    }
  }
  if (same)
    return user.getName();
  else
    return cgers.get(0);
}

function GetCgerName() {
  //获取采购组内全体用户名称
  var roleid = getRoleIdByName(RoleCger());
  var users = getUsersByRoleId(roleid);
  var result = createObject("java.util.ArrayList");
  for (var iter = users.iterator(); users != null && iter.hasNext();) {
    var vo = iter.next();
    result.add(vo.getName());
  }
  return result;
}

function isCger(userName) {
  //判断是否采购员
  var cgers = GetCgerName();
  if (cgers != null && cgers.size() > 0) {
    for (var j = 0; j < cgers.size(); j++) {
      var cger = cgers.get(j);
      if (isNotNull(cger) && cger.equals(userName)) {
        return true;
      }
    }
  }
  return false;
}

function MakeCGPurchaseBill(docProcess, formProcess, doc, childVos, cgerName) {
  //创建采购申请单
  if (childVos != null && childVos.size() > 0) {
    var billFormName = "GY_CG_01_Purchase_Main";    //表单名称
    var applicationId = getApplication();
    var Form = formProcess.doViewByFormName(billFormName, applicationId);
    var params = createParamsTable();
    var userLoginno = getUserLoginnoByUserName(cgerName);
    var user = getUserByLoginno(userLoginno);
    var mainDoc = docProcess.doNew(Form, user, params);
    var prefix = "GYPM-";
    var billNo = countNext2(prefix, true, true, true, 4);
    mainDoc.addStringItem("编号", billNo);
    mainDoc.addDateItem("申请日期", getToday());
    mainDoc.addStringItem("申请人", user.getName());
    var depName = GetDetaulDepartmentByCurrentUser();
    mainDoc.addStringItem("所在业务组", depName);
    mainDoc.addStringItem("运营平台", doc.getItemValueAsString("运营平台"));
    mainDoc.addStringItem("采购类型", doc.getItemValueAsString("采购类型"));
    mainDoc.addStringItem("采购用途", doc.getItemValueAsString("采购用途"));
    mainDoc.addStringItem("来源订单编号", doc.getItemValueAsString("编号"));
    mainDoc.addStringItem("申请备注", doc.getItemValueAsString("申请备注"));

    docProcess.doCreate(mainDoc);//创建
    mainDoc = LoadByIndexFieldValue("tlk_gy_cg_01_purchase_main", "ITEM_编号", billNo, true);
    //发起流程  
    var flowid = "__igWI9dtAgdiziMF1gU9";//获取流程id                            
    var billParams = createParamsTable();
    billParams.setParameter("_flowid", flowid);
    docProcess.doStartFlowOrUpdate(mainDoc, billParams, user); //启动流程   

    //采购申请单明细
    for (var j = 0; j < childVos.size(); j++) {
      var childVo = childVos.get(j);
      childVo.setParent(mainDoc.getId());
      docProcess.doUpdate(childVo);
    }

  }
}

//搜索待办供应商订单
function FindOrderBillNumberStantByOrder(SupplierName) {
  var sql = "SELECT * FROM tlk_gy_cg_01_order_main WHERE ITEM_供应商名称 = '" + SupplierName + "' " +
    " AND STATELABEL = '采购组采购' ORDER BY ITEM_编号 DESC";
  var data = MagicFindBySql("magic", sql);
  if (data == null)
    return null;
  return data.get("ITEM_编号");
}

//判断当前采购申请单是否需要拆分采购
function IsNeedSplitSKU(doc) {
  var detailSql = "SELECT * FROM tlk_gy_cg_01_purchase_detail WHERE PARENT = '" + doc.getId() + "'";
  var skus = "";
  var query = queryBySQL(detailSql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var detailDoc = iter.next();
      if (isNotNull(skus))
        skus += "','";
      skus += detailDoc.getItemValueAsString("SKU") + "-K";
    }
  }
  skus = "'" + skus + "'";
  var existsSql = "select GoodsSKUID from SHOPELF.B_GOODSGROUP bgg " +
    "left join SHOPELF.B_Goods bg on bgg.GoodsID = bg.NID " +
    "where bg.SKU IN (" + skus + ")";
  var arr = MagicQueryBySql("DATACENTER_BUSINESS", existsSql);
  return arr.size() > 0;
}

//成品拆分成物料
function SplitSKU(parentSKU) {
  var result = createObject("java.util.ArrayList");
  var sql = "select bg.SKU, bg.SKU as ParentSKU, bgs.SKU as ChildSKU, bgg.Amount from SHOPELF.B_GOODSGROUP bgg " +
    "left join SHOPELF.B_Goods bg on bgg.GoodsID = bg.NID " +
    "left join SHOPELF.B_GOODSSKU bgs on bgg.GoodsSKUID = bgs.NID ";
  sql = "SELECT ParentSKU, ChildSKU, Amount FROM (" + sql + ") t " +
    "WHERE ParentSKU = CONCAT('" + parentSKU + "', '-K')";
  return MagicQueryBySql("DATACENTER_BUSINESS", sql);
}

function PaymentBillSplitSKU(docProcess, formProcess, pd, vos, user) {
  //把需要拆分的成品SKU的需求数置零
  var sourceChildSkuDocs = LoadChildDocs("tlk_gy_cg_01_purchase_detail", pd.getId());
  var setList = vos.keySet();
  for (var iter = setList.iterator(); iter.hasNext();) {
    var childSKUdoc = iter.next();
    //拆分前把选择的成品移到采购申请单拆分成品记录里
    BackUpComboGoods(docProcess, formProcess, user, pd, childSKUdoc);
    setPaymentGoodsZero(docProcess, sourceChildSkuDocs, childSKUdoc);
  }
  //添加物料SKU
  var updateList = vos.keySet();
  for (var uiter = updateList.iterator(); uiter.hasNext();) {
    var parentSKUDoc = uiter.next();
    var arr = vos.get(parentSKUDoc);
    if (arr != null && arr.size() > 0) {
      for (var j = 0; j < arr.size(); j++) {
        var childSKUDoc = arr.get(j);
        //判断是否已存在于采购申请单明细里
        var existsSql = "SELECT * FROM tlk_gy_cg_01_purchase_detail where PARENT = '" +
          pd.getId() + "' AND ITEM_SKU = '" + childSKUDoc.get("ChildSKU") + "'";
        var t_count = countBySQL(existsSql);
        if (t_count < 1)
          PaymentBillSplitSKUAdd(docProcess, formProcess, pd, parentSKUDoc, childSKUDoc, user);
        else
          PaymentBillSplitSKUUpdate(docProcess, formProcess, pd, parentSKUDoc, childSKUDoc, user);
      }
    }
  }
  //清理成品的确认下单数为零的SKU
  PaymentBillSplitSKUClear(docProcess, pd, vos);
}

function setPaymentGoodsZero(docProcess, sourceChildSkuDocs, childSKUDoc) {
  //把成品需求设置为零
  if (sourceChildSkuDocs != null && sourceChildSkuDocs.size() > 0) {
    for (var j = 0; j < sourceChildSkuDocs.size(); j++) {
      var vo = sourceChildSkuDocs.get(j);
      if (vo.getItemValueAsString("SKU").equals(childSKUDoc.getItemValueAsString("SKU"))) {
        vo.findItem("计划采购数").setValue(0);
        vo.findItem("确认下单数量").setValue(0);
        docProcess.doUpdate(vo);
      }
    }
  }
}

function PaymentBillSplitSKUAdd(docProcess, formProcess, pd, parentSKUDoc, childSKUDoc, user) {
  //添加物料                                                                                     
  var billFormName = "GY_CG_01_Purchase_Detail";
  var Form = formProcess.doViewByFormName(billFormName, getApplication());
  var params = createParamsTable();
  var doc = docProcess.doNew(Form, user, params);
  var amount = parentSKUDoc.getItemValueAsString("确认下单数量") * childSKUDoc.get("Amount");
  doc.addStringItem("SKU", childSKUDoc.get("ChildSKU"));
  doc.addDoubleItem("计划采购数", amount);
  doc.addDoubleItem("确认下单数量", amount);
  var logMsg = getAddMeterialLog(parentSKUDoc, childSKUDoc);
  doc.addStringItem("采购备注", logMsg);
  doc = PaymentBillSplitSKUAddTCGMsg(doc);
  doc.setParent(pd.getId());
  docProcess.doCreate(doc);
}

function getAddMeterialLog(parentSKUDoc, childSKUDoc) {
  //添加物料的日志
  var amount = parentSKUDoc.getItemValueAsDouble("确认下单数量") * childSKUDoc.get("Amount");
  return "来源于" + parentSKUDoc.getItemValueAsString("SKU") + "需求" +
    parentSKUDoc.getItemValueAsDouble("确认下单数量") + "件拆分成物料" + amount + "件";
}

function PaymentBillSplitSKUUpdate(docProcess, formProcess, pd, parentSKUDoc, childSKUDoc) {
  //更新物料
  var sql = "SELECT * FROM tlk_gy_cg_01_purchase_detail where PARENT = '" +
    pd.getId() + "' AND ITEM_SKU = '" + childSKUDoc.get("ChildSKU") + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var doc = iter.next();
      var adjustAmount = parentSKUDoc.getItemValueAsDouble("确认下单数量") * childSKUDoc.get("Amount");
      var planAmount = doc.getItemValueAsDouble("计划采购数");
      var comfirmAmount = doc.getItemValueAsDouble("确认下单数量");
      var memo = doc.getItemValueAsString("采购备注");
      if ("".equals(memo))
        memo = "原确认下单数量：" + comfirmAmount + "件";
      memo += "," + getAddMeterialLog(parentSKUDoc, childSKUDoc);
      doc.findItem("计划采购数").setValue(planAmount + adjustAmount);
      doc.findItem("确认下单数量").setValue(comfirmAmount + adjustAmount);
      doc.findItem("采购备注").setValue(memo);
      docProcess.doUpdate(doc);
    }
  }
}

function PaymentBillSplitSKUClear(docProcess, pd, vos) {
  //检测拆分后，所选成品的SKU的确认下单数量是否为空
  //如果为空删除该SKU
  var updateList = vos.keySet();
  for (var uiter = updateList.iterator(); uiter.hasNext();) {
    var parentSKUDoc = uiter.next();
    var data = findDocument(parentSKUDoc.getId()); //更新SKU信息
    if (data == null)
      continue;
    var amount = data.getItemValueAsDouble("确认下单数量");
    if (amount <= 0)
      docProcess.doRemove(data);
  }
}

function PaymentBillSplitSKUAddTCGMsg(doc) {
  //从T_CG表获取SKU指标
  var sku = doc.getItemValueAsString("SKU");
  var arr = GetGoodsMsgFromCGPlan(sku);
  if (arr == null || arr.size() < 1)
    return doc;
  var vo = arr.get(0);
  doc.findItem("商品名称").setValue(vo.get("SKUDescript"));
  doc.findItem("供应商").setValue(vo.get("SupplierName"));
  doc.findItem("采购员").setValue(vo.get("Purchaser"));
  doc.findItem("三十天销量").setValue(vo.get("SaleAmount30D"));
  doc.findItem("是否拆分采购").setValue(vo.get("IsSplitBuy"));
  doc.findItem("商品等级").setValue(vo.get("productgrade"));
  doc.findItem("历史平均每单订购数").setValue(vo.get("HisAvgQtyPerOrder"));
  doc.findItem("商品等级").setValue(vo.get("ProductGrade"));
  doc.findItem("当前本地库存").setValue(vo.get("CurrentLocalInventory"));
  doc.findItem("广州仓库存").setValue(vo.get("CurrentGZInventory"));
  doc.findItem("北海仓库存").setValue(vo.get("CurrentBHInventory"));
  doc.findItem("河北仓库存").setValue(vo.get("CurrentHBInventory"));
  doc.findItem("当前FBA库存").setValue(vo.get("CurrentFBAInventory"));
  doc.findItem("当前FBA库存售完月").setValue(vo.get("CurrentFBAInventorySOM"));
  doc.findItem("货期").setValue(vo.get("procurementcycle"));
  doc.findItem("采购在途").setValue(vo.get("stocknotinamount"));
  doc.findItem("FBA在途").setValue(vo.get("fbashippedamount"));
  return doc;
}

//拆分成品功能，备份成品记录
function BackUpComboGoods(docProcess, formProcess, user, parentDocument, detailDoc) {
  //创建采购订单明细                                                     
  var billFormName = "GY_CG_01_Purchase_Detail_Combo";
  var Form = formProcess.doViewByFormName(billFormName, getApplication());
  var params = createParamsTable();
  var doc = docProcess.doNew(Form, user, params);

  doc.addStringItem("编号", detailDoc.getItemValueAsString("编号"));
  doc.addStringItem("SKU", detailDoc.getItemValueAsString("SKU"));
  doc.addDoubleItem("三十天销量", detailDoc.getItemValueAsDouble("三十天销量"));
  doc.addStringItem("是否拆分采购", detailDoc.getItemValueAsString("是否拆分采购"));
  doc.addDoubleItem("计划采购数", detailDoc.getItemValueAsDouble("计划采购数"));
  doc.addDoubleItem("货期", detailDoc.getItemValueAsDouble("货期"));
  doc.addDoubleItem("历史平均每单订购数", detailDoc.getItemValueAsDouble("历史平均每单订购数"));
  doc.addStringItem("商品等级", detailDoc.getItemValueAsString("商品等级"));
  doc.addStringItem("是否FBA", detailDoc.getItemValueAsString("是否FBA"));
  doc.addStringItem("采购备注", detailDoc.getItemValueAsString("采购备注"));
  doc.addStringItem("采购员", detailDoc.getItemValueAsString("采购员"));
  doc.addDoubleItem("确认下单数量", detailDoc.getItemValueAsDouble("确认下单数量"));
  doc.addDoubleItem("参考总采购数", detailDoc.getItemValueAsDouble("参考总采购数"));
  doc.addDoubleItem("当前本地库存", detailDoc.getItemValueAsDouble("当前本地库存"));
  doc.addDoubleItem("当前FBA库存", detailDoc.getItemValueAsDouble("当前FBA库存"));
  doc.addStringItem("供应商", detailDoc.getItemValueAsString("供应商"));
  doc.addStringItem("商品名称", detailDoc.getItemValueAsString("商品名称"));
  doc.addStringItem("颜色或型号", detailDoc.getItemValueAsString("颜色或型号"));
  doc.addStringItem("采购规格", detailDoc.getItemValueAsString("采购规格"));
  doc.addStringItem("销售规格", detailDoc.getItemValueAsString("销售规格"));
  doc.addStringItem("SKU系列", detailDoc.getItemValueAsString("SKU系列"));
  doc.addDoubleItem("最低起订量", detailDoc.getItemValueAsDouble("最低起订量"));
  doc.addStringItem("采购链接", detailDoc.getItemValueAsString("采购链接"));
  doc.addStringItem("运营平台", detailDoc.getItemValueAsString("运营平台"));
  doc.addDoubleItem("七天销量", detailDoc.getItemValueAsDouble("七天销量"));
  doc.addDoubleItem("单价", detailDoc.getItemValueAsDouble("单价"));
  doc.addDoubleItem("金额", detailDoc.getItemValueAsDouble("金额"));
  doc.addDoubleItem("运费", detailDoc.getItemValueAsDouble("运费"));
  doc.addStringItem("预计到货日期", detailDoc.getItemValueAsString("预计到货日期"));
  doc.addStringItem("上架日期", detailDoc.getItemValueAsString("上架日期"));
  doc.addDoubleItem("上架天数", detailDoc.getItemValueAsDouble("上架天数"));
  doc.addDoubleItem("上架至今总销量", detailDoc.getItemValueAsDouble("上架至今总销量"));
  doc.addDoubleItem("广告出单量", detailDoc.getItemValueAsDouble("广告出单量"));
  doc.addDoubleItem("自然订单量", detailDoc.getItemValueAsDouble("自然订单量"));
  doc.addDoubleItem("SKU在售时日均销量", detailDoc.getItemValueAsDouble("SKU在售时日均销量"));
  doc.addDoubleItem("系列日均销量", detailDoc.getItemValueAsDouble("系列日均销量"));
  doc.addDoubleItem("主SKU库存数", detailDoc.getItemValueAsDouble("主SKU库存数"));
  doc.addDoubleItem("现有库存可组合数", detailDoc.getItemValueAsDouble("现有库存可组合数"));
  doc.addStringItem("采购用途", detailDoc.getItemValueAsString("采购用途"));
  doc.addStringItem("缺货来源", detailDoc.getItemValueAsString("缺货来源"));
  doc.addDateItem("预计生产时长", detailDoc.getItemValueAsDate("预计生产时长"));
  doc.addDoubleItem("当前FBA库存售完月", detailDoc.getItemValueAsDouble("当前FBA库存售完月"));
  doc.addStringItem("附件", detailDoc.getItemValueAsString("附件"));
  doc.addDoubleItem("采购在途", detailDoc.getItemValueAsDouble("采购在途"));
  doc.addDoubleItem("FBA在途", detailDoc.getItemValueAsDouble("FBA在途"));
  doc.addDoubleItem("首批上架数量", detailDoc.getItemValueAsDouble("首批上架数量"));
  doc.addDoubleItem("ALI1688三十天销量", detailDoc.getItemValueAsDouble("ALI1688三十天销量"));
  doc.addDoubleItem("速卖通三十天销量", detailDoc.getItemValueAsDouble("速卖通三十天销量"));
  doc.addDoubleItem("广州仓库存", detailDoc.getItemValueAsDouble("广州仓库存"));
  doc.addDoubleItem("北海仓库存", detailDoc.getItemValueAsDouble("北海仓库存"));
  doc.addDoubleItem("河北仓库存", detailDoc.getItemValueAsDouble("河北仓库存"));
  doc.addStringItem("停售", detailDoc.getItemValueAsString("停售"));
  doc.addStringItem("采购仓库", detailDoc.getItemValueAsString("采购仓库"));
  doc.addStringItem("责任归属人", detailDoc.getItemValueAsString("责任归属人"));
  doc.addStringItem("业绩归属人", detailDoc.getItemValueAsString("业绩归属人"));

  doc.setParent(parentDocument.getId());
  docProcess.doCreate(doc);//创建                            

}

//计算采购申请单中商品的最近一次采购价格，如果存在组合品，拆成物料再算价格
function CGApplyTotalPrice(doc) {
  var sql = "select * from tlk_gy_cg_01_purchase_detail where PARENT = '" + doc.getId() + "'";
  var query = queryBySQL(sql);
  var result = 0;
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      var sku = data.getItemValueAsString("SKU");
      var amount = data.getItemValueAsDouble("确认下单数量");
      var price = CGApplyGetLastPrice(sku);
      if (price == 0)
        price = CGApplyGetGroupPrice(sku);
      result += price * amount;
    }
  }
  return result;
}

function CGApplyGetLastPrice(sku) {
  //获取单个SKU的最近一次采购订单价格
  var sql = "select tabA.Price from ( " +
    "    select " +
    "t.GoodsID AS GoodsID, " +
    "t.TaxPrice AS Price, " +
    "row_number() OVER (PARTITION BY t.GoodsID " +
    "ORDER BY " +
    "t.StockOrderNID desc ) AS rn " +
    "from " +
    "SHOPELF.CG_STOCKORDERD t " +
    "where " +
    "    ((t.TaxPrice > 0) " +
    "        and (t.TaxPrice is not null))) tabA  " +
    "        left join B_GOODS bg on tabA.GoodsID = bg.NID  " +
    "        where tabA.rn = 1 AND bg.SKU = '" + sku + "'";
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  if (data != null) {
    return data.get("Price");
  }
  return 0;
}

function HaveZeroPrice(doc) {
  var sql = "select * from tlk_gy_cg_01_purchase_detail where PARENT = '" + doc.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      var sku = data.getItemValueAsString("SKU");
      var price = CGApplyGetLastPrice(sku);
      if (price == 0)
        return true;
    }
  }
  return false;
}

function CGApplyGetGroupPrice(sku) {
  //获取组合品的物料总采购价格
  var sql = "select bgs.SKU, bgg.Amount from SHOPELF.B_GOODSGROUP bgg " +
    "left join SHOPELF.B_Goods bg on bgg.GoodsID = bg.NID  " +
    "left join SHOPELF.B_GOODSSKU bgs on bgg.GoodsSKUID = bgs.NID  " +
    "where bg.SKU = '" + sku + "-K'";
  var arr = MagicQueryBySql("DATACENTER_BUSINESS", sql);
  var result = 0;
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var vo = arr.get(j);
      var childSKU = vo.get("SKU");
      var amount = vo.get("Amount");
      var price = CGApplyGetLastPrice(childSKU);
      if (price == 0)
        price = CGApplyGetGroupPrice(childSKU);
      result += price * amount;
    }
  }
  return result;
}

//获取历史最低价格
function HistoricalLowestPrice(sku, makeDate) {
  var sql = ReadSql("HistoricalLowestPrice");
  sql = sql.replace("{SKU}", sku);
  sql = sql.replace("{MakeDate}", makeDate);
  debug("HistoricalLowestPrice.sql=" + sql);
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  if (data != null)
    return data.get("MinPrice");
  else
    return 0;
}

//判断组装任务的物料是否足够，足够就开始组装
function AssemblyIsWorking(doc) {
  var sql = "select count(1) as Amount  from BUSINESS.PDI_OA_ASSEMBLYTASK_GROUPAMOUNT " +
    "WHERE NEW_GROUPAMOUNT >0 AND BILLNUMBER = '" + doc.getItemValueAsString("编号") + "'";
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  if (data != null)
    return data.get("Amount") > 0;
  return false;
}

//查看成品SKU所需物料的库存状态
function AssemblyMetrialMsg(doc) {
  var sku = doc.getItemValueAsString("成品SKU");
  var storeName = TransStoreName(doc.getItemValueAsString("组装仓库"));
  var sql = ReadSql("GY_ST_03_Assembly_GoodsGroup");
  sql = ReplaceAll(sql, "DOMAINID", getDomainid());
  sql = ReplaceAll(sql, "PARENT", doc.getId());
  sql = ReplaceAll(sql, "SKU", sku);
  sql = ReplaceAll(sql, "StoreName", storeName);
  println(sql);
  sql = "SELECT TableA.* FROM (" + sql + ") AS TableA ORDER BY ITEM_GroupAmount ";
  return sql;
}

//通过整合单计算组装任务中已整合的数量
function AssemblySumAlreadyGroupAmount(pd) {
  var sql = "SELECT SUM(ITEM_整合数量) AS ITEM_AMOUNT FROM tlk_gy_st_03_assemblylist WHERE PARENT = '" + pd.getId() + "'";
  return sumBySQL(sql, "AMOUNT");

  var query = queryBySQL(zhdSql);
  var zhd = "";
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      if ("".equals(zhd) == false)
        zhd += ",";
      zhd += "'" + data.getItemValueAsString("普源整合单号") + "'";
    }
  }
  if (isNotNull(zhd) == false)
    return 0;
  var sql = ReadSql("GY_ST_03_Assembly_ZHD_Detail");
  sql = ReplaceAll(sql, "DOMAINID", getDomainid());
  sql = ReplaceAll(sql, "PARENT", pd.getId());
  sql = ReplaceAll(sql, "SKU", pd.getItemValueAsString("成品SKU"));
  sql = ReplaceAll(sql, "ZHD", zhd);
  println(sql);
  sql = "SELECT SUM(ITEM_数量) as Amount from (" + sql + ") as TableA";
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  return data.get("Amount");
}

//通过采购订单生成采购付款申请单
//CGOrder = getCurrentDocument();
//user = getWebUser();
//var docProcess = getDocProcess(applicationId);     
//var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();  
function CreatePaymentByOrder(CGOrder, user, docProcess, formProcess) {
  var currentDoc = CGOrder;
  var applicationId = getApplication();//获取applicationId                                                                 
  //创建付款申请订单                                     
  var cgdPrefix = "GYPayM-";
  var cgBillNo = countNext2(cgdPrefix, true, true, true, 4);
  var billFormName = "GY_CG_02_Payment";
  var Form = formProcess.doViewByFormName(billFormName, applicationId);//（'GY_CG_01_Order_Main'为表单名称）                                     
  var params = createParamsTable();
  var cgerName = currentDoc.getItemValueAsString("采购员");
  var cger = getUserByLoginno(getUserLoginnoByUserName(cgerName));
  var paymentMainDoc = docProcess.doNew(Form, cger, params);
  paymentMainDoc.addStringItem("编号", cgBillNo);
  paymentMainDoc.addStringItem("申请人", currentDoc.getItemValueAsString("采购员"));
  paymentMainDoc.addDateItem("申请日期", getToday());
  paymentMainDoc.addStringItem("采购订单编号", currentDoc.getItemValueAsString("编号"));
  paymentMainDoc.addStringItem("商家订单号", currentDoc.getItemValueAsString("ALI1688订单号"));
  paymentMainDoc.addStringItem("供应商", currentDoc.getItemValueAsString("供应商名称"));
  paymentMainDoc.addStringItem("支付对象", currentDoc.getItemValueAsString("支付对象"));
  paymentMainDoc.addStringItem("开户银行", currentDoc.getItemValueAsString("开户银行"));
  paymentMainDoc.addStringItem("收款账号", currentDoc.getItemValueAsString("银行账号"));
  paymentMainDoc.addStringItem("付款方式", currentDoc.getItemValueAsString("支付方式"));
  var packageFee = SumPackageFee(currentDoc);
  paymentMainDoc.addDoubleItem("分装费", packageFee);
  //paymentMainDoc.addStringItem("本次付款金额", 0);               
  //paymentMainDoc.addStringItem("款项类别", currentDoc.getItemValueAsString(""));               
  paymentMainDoc.addStringItem("备注",
    currentDoc.getItemValueAsString("采购申请单备注") +
    currentDoc.getItemValueAsString("采购申请单备注"));
  paymentMainDoc.addStringItem("采购申请单", currentDoc.getItemValueAsString("采购申请单号"));
  paymentMainDoc.addStringItem("普源采购订单编号", currentDoc.getItemValueAsString("普源采购订单编号"));
  var totalMoney = sumSubDocument("GY_CG_01_Order_Detail", "金额");
  paymentMainDoc.addDoubleItem("采购订单金额", totalMoney);
  docProcess.doCreate(paymentMainDoc);//保存                      
  //重新获取采购订单对象                                     
  paymentMainDoc = LoadByIndexFieldValue("tlk_gy_cg_02_payment", "ITEM_编号", cgBillNo, true);
  var flowid = "__O2FxkEdmEbi6JCiWESe";//获取流程id                                     
  var billParams = createParamsTable();
  billParams.setParameter("_flowid", flowid);
  docProcess.doStartFlowOrUpdate(paymentMainDoc, billParams, cger); //启动流程                                      
  SyncPriceChange(currentDoc);
}

function SyncPriceChange(doc) {
  var sql = "SELECT * FROM tlk_gy_cg_02_payment_price_change_for_order WHERE PARENT = '" + doc.getId() + "'";
  var query = queryBySQL(sql);
  var applicationId = getApplication();
  if (query != null) {
    var billFormName = "GY_CG_02_Payment_Price_Change";
    var docProcess = getDocProcess(getApplication());
    var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
    var Form = formProcess.doViewByFormName(billFormName, applicationId);//（'GY_CG_01_Order_Main'为表单名称）                                     
    var params = createParamsTable();
    var cgerName = doc.getItemValueAsString("采购员");
    var cger = getUserByLoginno(getUserLoginnoByUserName(cgerName));
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      var newDoc = docProcess.doNew(Form, cger, params);
      newDoc.addStringItem("SKU", data.getItemValueAsString("SKU"));
      newDoc.addStringItem("商品名称", data.getItemValueAsString("商品名称"));
      newDoc.addDoubleItem("采购价格", data.getItemValueAsDouble("采购价格"));
      newDoc.addDoubleItem("最近一次采购价格", data.getItemValueAsDouble("最近一次采购价格"));
      newDoc.addDoubleItem("变动幅度", data.getItemValueAsDouble("变动幅度"));
      newDoc.addStringItem("价格变动原因", data.getItemValueAsString("价格变动原因"));
      newDoc.addStringItem("复核", data.getItemValueAsString("复核"));
      newDoc.setParent(doc.getId());
      docProcess.doCreate(newDoc);//创建       
    }
  }
}

//统计采购订单分装费
function SumPackageFee(order) {
  var sql = "select sum(ITEM_分装费) AS ITEM_分装费 from tlk_gy_cg_01_order_detail WHERE PARENT = '" + order.getId() + "'";
  return sumBySQL(sql, "分装费");
}

//通过采购申请单创建组装任务
function GenerateAssemblyTaskByCGBill(docProcess, form, doc) {
  var sql = "SELECT * FROM tlk_gy_cg_01_purchase_detail_combo WHERE PARENT = '" + doc.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      GenerateAssemblyTaskByProduct(docProcess, form, doc, data);
    }
  }
}

function GenerateAssemblyTaskByProduct(docProcess, form, pd, goods) {
  if (IsExistsAssembyTask(pd, goods))
    return;
  var cgerName = goods.getItemValueAsString("采购员");
  var cgerId = getUserLoginnoByUserName(cgerName);
  if (isNotNull(cgerId) == false) {
    cgerId = getUserLoginnoByUserName(CG_DefaultCgerName());
  }
  var cger = getUserByLoginno(cgerId);
  //当通采购申请单的成品创建组装任务                        
  var cgdPrefix = "GYST-";
  var cgBillNo = countNext2(cgdPrefix, true, true, true, 4);

  var params = createParamsTable();
  var doc = docProcess.doNew(form, cger, params);
  doc.addStringItem("编号", cgBillNo);
  doc.addStringItem("填写人", cger.getName());
  doc.addDateItem("填写日期", getToday());
  doc.addStringItem("来源单号", pd.getItemValueAsString("编号"));
  doc.addStringItem("成品SKU", goods.getItemValueAsString("SKU"));
  doc.addStringItem("组装说明", goods.getItemValueAsString("组装说明"));
  doc.addIntItem("应组数量", goods.getItemValueAsString("确认下单数量"));
  doc.addIntItem("已组数量", 0);
  doc.addIntItem("可组数量", 0);
  var storeName = goods.getItemValueAsString("采购仓库");
  if (isNotNull(storeName) == false)
    storeName = GetStoreBySupplierName(goods.getItemValueAsString("供应商"));
  doc.addStringItem("组装仓库", storeName);
  doc.addStringItem("发起方式", "系统发起");
  doc.addStringItem("优先级", "普通");
  doc.addStringItem("备注", goods.getItemValueAsString("采购备注"));
  doc.addStringItem("来源类型", "采购申请单");
  docProcess.doCreate(doc);//创建                            
  //重新获取采购订单对象                            
  doc = LoadByIndexFieldValue("tlk_gy_st_03_assemblytask", "ITEM_编号", cgBillNo, true);
  var flowid = "__WWjbROLcJEvufcqKfxJ";
  var billParams = createParamsTable();
  billParams.setParameter("_flowid", flowid);
  docProcess.doStartFlowOrUpdate(doc, billParams, cger); //启动流程 
}

function IsExistsAssembyTask(pd, goods) {
  var sql = "SELECT * FROM tlk_gy_st_03_assemblytask " +
    "WHERE ITEM_来源单号 = '" + pd.getItemValueAsString("编号") + "' AND ITEM_成品SKU = '" + goods.getItemValueAsString("SKU") + "'";

  var t_count = countBySQL(sql);
  return t_count > 0;
}

function GetStoreBySupplierName(SupplierName) {
  //通过供应商口称获取采购（组装）仓库名称
  if ("新乐外协".equals(SupplierName))
    return "河北仓";
  else if ("北海外协".equals(SupplierName))
    return "北海仓";
  else
    return "广州仓";
}

//通过SKU获取组装或采购仓库
function GetStoreBySKU(SKU) {
  if (isNotNull(SKU) == false)
    return;
  var vo = GetTCGMsg(SKU);
  if (isNotNull(vo)) {
    var supplierName = vo.get("SupplierName");
    var storeName = GetStoreBySupplierName(supplierName);
    return storeName;
  }
}

//组装任务审批流程中，对用途为活动推广、新品推广的申请判断是否需要审批
function AssemblyTaskPurposeSign(doc) {
  var purpose = doc.getItemValueAsString("用途");
  var sign = doc.getItemValueAsString("运营审批");
  //用途为活动推广或新品推广且运营审批为空时  
  if ("活动推广".equals(purpose) ||
    "新品推广".equals(purpose)) {
    return isNotNull(sign) == false;
  } else {
    return false;
  }
}

//备份拆分成品前的采购明细数据
function BackupCGDetail(docProcess, form, user, pd) {
  ClearBackupCGDetail(pd);
  var sql = "SELECT * FROM tlk_gy_cg_01_purchase_detail WHERE PARENT = '" + pd.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    println("execute sql=" + sql);
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      var params = createParamsTable();
      var newDoc = docProcess.doNew(form, user, params);
      debug("data=" + data);
      debug("newDoc=" + newDoc);
      CloneDoc(data, newDoc);
      newDoc.setParent(pd.getId());
      docProcess.doCreate(newDoc);//创建 
    }
  }
}

function ClearBackupCGDetail(doc) {
  //避免重复写入备份信息，写入备份前先清空之前的备份;
  var sql = "DELETE FROM tlk_gy_cg_01_purchase_detail_source WHERE PARENT = '" + doc.getId() + "'";
  println("ClearBackupCGDetail:" + sql);
  deleteByDSName("magic", sql);
}

//更新组装任务可组数量
function JobAutoUpdateAssemTaskAmount() {
  var docProcess = getDocProcess(getApplication());
  //更新组装任务的可组数量
  UpdateCanAssemblyAmount(docProcess);
  //更新已组数量
  UpdateAssemblyGroupAmount(docProcess);
}

function UpdateAssemblyList(docProcess, taskDoc) {
  //更新组装任务整合单表的单据状态
  var sql = "SELECT * FROM tlk_gy_st_03_assemblylist WHERE PARENT = '" + taskDoc.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var listDoc = iter.next();
      var billNumber = listDoc.getItemValueAsString("普源整合单号");
      //debug("UpdateAssemblyList:" + billNumber);
      UpdateZHDMsg(listDoc);
      docProcess.doUpdate(listDoc);
    }
  }
}

function UpdateZHDMsg(doc) {
  //更新组装任务整合单表的单据状态
  var billNumber = doc.getItemValueAsString("普源整合单号");
  var sql = "SELECT * FROM SHOPELF.KC_STOCKSPLITM WHERE BillNumber = '" + billNumber + "'";
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  if (isNotNull(data)) {
    doc.findItem("制单人").setValue(data.get("Recorder"));
    doc.findItem("制单时间").setValue(data.get("MakeDate"));
    doc.findItem("审核人").setValue(data.get("Audier"));
    doc.findItem("审核时间").setValue(data.get("AudieDate"));
    doc.findItem("备注").setValue(data.get("Memo"));
    //debug("更新:" + billNumber);
  }
}

function GetAssembleAmount(task) {
  //汇总整合单整合的数量
  var sql = "SELECT SUM(ITEM_整合数量) AS ITEM_Amount FROM tlk_gy_st_03_assemblylist " +
    "WHERE PARENT = '" + task.getId() + "'";
  return sumBySQL(sql, "Amount");
}

function AlreadyAssembleAmount(BillNumber, SKU) {
  //整合单，整合的数量
  var sql = "SELECT sm.BillNumber, bg.SKU, SUM(td.Amount) AS Amount FROM SHOPELF.KC_STOCKSPLITM sm " +
    "left join SHOPELF.CG_StockInM tm on sm.NID = tm.StockSplitNid " +
    "left join SHOPELF.CG_STOCKIND td on tm.NID = td.StockInNID " +
    "left join SHOPELF.B_Goods bg on td.GoodsID = bg.NID " +
    "group by sm.BillNumber, bg.SKU " +
    "where sm.BillNumber = '" + BillNumber + "' AND bg.SKU = '" + sku + "'";
  var data = MagicFindBySql("DATACENTER_SHOPELF", sql);
  if (data == null)
    return 0;
  else
    return data.get("Amount");
}


function UpdateAssemblyGroupAmount(docProcess) {
  //更新组装任务的已组数量
  var mainSql = "SELECT tm.ID, SUM(COALESCE(td.ITEM_整合数量, 0)) as AMOUNT FROM tlk_gy_st_03_assemblytask tm " +
    "left join tlk_gy_st_03_assemblylist td on tm.ID = td.PARENT " +
    "WHERE tm.STATELABEL NOT IN ('', '结束', '作废') " +
    "group by tm.ID";
  var arr = MagicQueryBySql("magic", mainSql);
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var data = arr.get(j);
      var doc = findDocument(data.get("ID"));
      if (isNotNull(doc)) {
        doc.findItem("已组数量").setValue(data.get("AMOUNT"));
        docProcess.doUpdate(doc);
      }
    }
  }
}

function UpdateCanAssemblyAmount(docProcess) {
  //更新可组数量
  var sql = ReadSql("Job物料备货发起组装任务");
  var applicationId = SysApplicationID();//获取applicationId                            
  var docProcess = getDocProcess(applicationId);
  debug("UpdateCanAssemblyAmount.sql=" + sql);
  var arr = MagicQueryBySql("DATACENTER_BUSINESS", sql);
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var data = arr.get(j);
      var amount = data.get("NEW_GROUPAMOUNT");
      var doc = findDocument(data.get("id"));
      if (isNotNull(doc)) {
        debug("组装任务自动填写已组数量：编号=" + doc.getItemValueAsString("编号") + ", 已组数量更新为" + amount);
        doc.findItem("可组数量").setValue(amount);
        docProcess.doUpdate(doc);
      }
    }
  } else {
    debug("UpdateCanAssemblyAmount没有数据");
  }
}

//采购类型是不是首次采购
function IsFirstBuyKind(kind) {
  if ("新品首次采购".equals(kind) ||
    "旧系列拓展SKU首采".equals(kind) ||
    "旧系列升级SKU首采".equals(kind) ||
    "自主生产系列首采".equals(kind)) {
    return true;
  } else {
    return false;
  }
}

//拆分物料后，需还原首采商品信息
function FillSourceMsgForFirstBuy(pd) {
  var sql = "SELECT * FROM tlk_gy_cg_01_purchase_detail WHERE PARENT = '" + pd.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    var docProcess = getDocProcess(getApplication());
    for (var iter = query.iterator(); iter.hasNext();) {
      var doc = iter.next();
      var sku = doc.getItemValueAsString("SKU");
      var sourceSql = "SELECT * FROM tlk_gy_cg_01_purchase_detail_source " +
        "WHERE PARENT = '" + pd.getId() + "' " +
        "AND ITEM_SKU = '" + sku + "'";
      var sourceDoc = MagicFindBySql("magic", sourceSql);
      if (isNotNull(sourceDoc)) {
        doc.findItem("颜色或型号").setValue(sourceDoc.get("ITEM_颜色或型号"));
        doc.findItem("采购规格").setValue(sourceDoc.get("ITEM_采购规格"));
        doc.findItem("销售规格").setValue(sourceDoc.get("ITEM_销售规格"));
        doc.findItem("采购链接").setValue(sourceDoc.get("ITEM_采购链接"));
        doc.findItem("单价").setValue(sourceDoc.get("ITEM_单价"));
        doc.findItem("金额").setValue(sourceDoc.get("ITEM_金额"));
        doc.findItem("采购员").setValue(sourceDoc.get("ITEM_采购员"));
        doc.findItem("责任归属人").setValue(sourceDoc.get("ITEM_责任归属人"));
        doc.findItem("业绩归属人").setValue(sourceDoc.get("ITEM_业绩归属人"));
        doc.findItem("采购备注").setValue(sourceDoc.get("ITEM_采购备注"));
        docProcess.doUpdate(doc);
      } else {

      }
    }
  } else {

  }
}

//校验验采购申请单中商品明细是否包含淘汰商品
//返回空字符串表示校验通过（不包含淘汰商品） 否则返回所有淘汰商品的SKU
function ValidPurchaseBillSKUGrade(doc) {
  var sql = "SELECT * FROM magic5.tlk_gy_cg_01_purchase_detail WHERE PARENT = '" + doc.getId() + "' " +
    "AND ITEM_商品等级 = '淘汰'";
  var result = MagicGetItemValue("magic", sql, "ITEM_SKU", false, true);
  return result;
}

//获取OA在途数量
function GetOANotInStock(sku) {
  var oaSql = ReadSql("GY_AN_OANotInStock");
  oaSql = ReplaceAll(oaSql, "SKU", sku);
  var oaDoc = MagicFindBySql("magic", oaSql);
  if (isNotNull(oaDoc))
    return oaDoc.get("AMOUNT");
  else
    return 0;
}

//获取待组装数量
function GetAssemblyAmount(sku) {
  var sql = "select tm.ITEM_成品SKU, SUM(tm.ITEM_应组数量 - tm.ITEM_已组数量) as ITEM_AMOUNT " +
    "from tlk_gy_st_03_assemblytask tm where STATELABEL <> '' and STATELABEL <> '结束' and STATELABEL <> '作废' " +
    "and tm.ITEM_成品SKU = '" + sku + "' group by tm.ITEM_成品SKU ";
  return sumBySQL(sql, "AMOUNT");
}

//组装任务
//在普源系统里查找未填写到OA组装任务的整合单
//自动填写到整合任务中，并更新已组数量，如果已组数量等于应组数量时自动提交到结束。
function JobAutoFillZHD() {
  var applicationId = SysApplicationID();
  var docProcess = getDocProcess(applicationId);
  var billFormName = "GY_ST_03_AssemblyList";
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
  var form = formProcess.doViewByFormName(billFormName, applicationId);
  //加载2025年所有普源整合单
  var pysql = ReadSql("Job_GY_ST_03_Assembly_AutoFill_ShopElf_StockSplit");
  var pyDatas = MagicQueryBySql("DATACENTER_SHOPELF", pysql);
  if (pyDatas != null && pyDatas.size() > 0) {
    for (var j = 0; j < pyDatas.size(); j++) {
      var pyData = pyDatas.get(j);
      var zhdAmount = pyData.get("Amount");
      var regAmount = GetZHDRegAmount(pyData);
      var amount = zhdAmount - regAmount;
      if (amount > 0) {
        //在OA组装任务中登记自动填写整合单，按任务创建时间排序
        FillToTasks(docProcess, form, pyData, amount);
      } else {
        //debug("amount <= 0");
        //debug("pysql=" + pysql);
      }
    }
  }
}

function PyStoreNameToOa(storeName) {
  if ("广州仓库".equals(storeName))
    return "广州仓";
  else
    return storeName;
}

function GetZHDRegAmount(pyData) {
  //计算出还有pyData中SKU还有多少没有登记在OA组装任务
  var billNumber = pyData.get("BillNumber");
  var sku = pyData.get("SKU");
  var sql = ReadSql("Job_GY_ST_03_Assembly_AutoFill_ZHD_RegAmount");
  sql = ReplaceAll(sql, "SKU", sku);
  sql = ReplaceAll(sql, "BillNumber", billNumber);
  var data = MagicFindBySql("magic", sql);
  if (isNotNull(data)) {
    return data.get("ITEM_OA登记数量");
  } else
    return 0;
}

function FillToTasks(docProcess, form, pyData, amount) {
  var pybillNumber = pyData.get("BillNumber");
  var makeDate = pyData.get("MakeDate");
  var storeName = PyStoreNameToOa(pyData.get("StoreName"));
  var sku = pyData.get("SKU");
  //获取未完全登记的OA组装任务
  var sql = ReadSql("Job_GY_ST_03_Assembly_AutoFill_OA");
  sql = ReplaceAll(sql, "SKU", sku);
  sql = ReplaceAll(sql, "StoreName", storeName);
  sql = ReplaceAll(sql, "MakeDate", makeDate);
  var arr = MagicQueryBySql("magic", sql);
  var result = new Packages.java.util.HashMap();

  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      if (amount <= 0) {
        break;
      }
      var data = arr.get(j);
      var oaBillNumber = data.get("ITEM_编号");
      var partAmount = data.get("ITEM_待组数量");
      if (amount >= partAmount) {
        AddZHD(docProcess, form, oaBillNumber, pybillNumber, partAmount);
        amount -= partAmount;
      } else {
        AddZHD(docProcess, form, oaBillNumber, pybillNumber, amount);
        amount = 0;
      }
    }
  }
  return result;
}

function AddZHD(docProcess, form, oaBillNumber, pybillNumber, amount) {
  //把整合单填写到OA组装任务
  debug("OA=" + oaBillNumber + ",py=" + pybillNumber + ",amount=" + amount);
  var user = GetAssistant();
  debug("AddZHD 1");
  var sql = "select ID from tlk_gy_st_03_assemblytask where ITEM_编号 = '" + oaBillNumber + "'";
  var doc = MagicFindBySql("magic", sql);
  var oaDoc = findDocument(doc.get("ID"));
  debug("AddZHD 2");
  if (isNotNull(oaDoc) == false) {
    return;
  }
  var params = createParamsTable();
  var doc = docProcess.doNew(form, user, params);
  doc.addStringItem("普源整合单号", pybillNumber);
  doc.addDoubleItem("整合数量", amount);
  doc = UpdateZHDMsg(pybillNumber, doc);
  doc.setParent(oaDoc.getId());
  debug("AddZHD 3");
  docProcess.doCreate(doc);
  debug("AddZHD 4");
  //更新组装任务中的已组数量
  UpdateAsmTaskAmount(docProcess, oaDoc);
  debug("AddZHD 5");
  InterveneFlowAsmDoc(oaDoc);
  debug("AddZHD 6");
}

function UpdateAsmTaskAmount(docProcess, doc) {
  //更新整合任务的已组数量
  var sql = "SELECT SUM(ITEM_整合数量) as Amount FROM tlk_gy_st_03_assemblylist WHERE PARENT = '" + doc.getId() + "' ";
  var data = MagicFindBySql("magic", sql);
  doc.findItem("已组数量").setValue(data.get("Amount"));
  docProcess.doUpdate(doc);
}

function InterveneFlowAsmDoc(doc) {
  //判断该任务已全部组装，如果全部组装就提交到结束
  if (doc.getItemValueAsDouble("应组数量") == doc.getItemValueAsDouble("已组数量")) {
    //干预到结束
    //InterveneFlow(doc, "罗杰城",  "第二步", ["第三步"],  "系统自动流转");
    InterveneFlow(doc, "智能助手", doc.getStateLabel(), ["结束"], "组装任务已完成，流程干预：结束");
  }
}

function UpdateZHDMsg(billNumber, doc) {
  if (isNotNull(billNumber) == false)
    return doc;
  var sql = "SELECT * FROM SHOPELF.KC_STOCKSPLITM WHERE BillNumber = '" + billNumber + "'";
  var data = MagicFindBySql("DATACENTER_SHOPELF", sql);
  if (isNotNull(data)) {
    doc.findItem("制单人").setValue(data.get("Recorder"));
    doc.findItem("制单时间").setValue(data.get("MakeDate"));
    doc.findItem("审核人").setValue(data.get("Audier"));
    doc.findItem("审核时间").setValue(data.get("AudieDate"));
    doc.findItem("备注").setValue(data.get("Memo"));
  }
  return doc;
}

//采购订单作废时 
//检测相关SKU在采购申请单生成的组装任是否存 
//如果有则把该组装任务干预到作废 
//并且把相关的AssemblyList记录进行删除 
function voidAssemblyTaskByOrder(doc) {
  var docProcess = getDocProcess(getApplication());
  //采购申请单编号 
  var purchaseBillNumber = doc.getItemValueAsString("采购申请单号");
  var assemblySql = "select DISTINCT tam.* from tlk_gy_cg_01_order_main tom " +
    "left join tlk_gy_cg_01_order_detail tod on tom.ID = tod.PARENT  " +
    "left join tlk_gy_cg_01_purchase_main tpm on tpm.ITEM_编号 = tom.ITEM_采购申请单号 " +
    "left join tlk_gy_cg_01_purchase_detail_combo tpd on tpm.ID = tpd.PARENT " +
    "left join tlk_gy_st_03_assemblytask tam on tam.ITEM_来源单号 = tpm.ITEM_编号 and tam.ITEM_成品SKU = tpd.ITEM_SKU and locate(tpd.ITEM_SKU, tod.ITEM_备注) " +
    "where tom.ITEM_编号 = '" + doc.getItemValueAsString("编号") + "' and tam.ITEM_成品SKU is not null";
  debug("assemblyTaskSql=" + assemblySql);
  var query = queryBySQL(assemblySql);
  if (query != null) {
    var delMsg = doc.getItemValueAsString("编号") + "采购订单作废，自动作废组装任务。";
    for (var iter = query.iterator(); iter.hasNext();) {
      var taskDoc = iter.next();
      var user = taskDoc.getAuthor();
      debug(taskDoc.getItemValueAsString("编号") + "准备删除assemblyTask.");
      deleteTaskZHD(docProcess, taskDoc);
      debug("重启流程：" + taskDoc.getItemValueAsString("编号"));
      //Restart(taskDoc, "创建组装任务");
      InterveneFlow(taskDoc, "智能助手", taskDoc.getStateLabel(), ["作废"], delMsg);
    }
  } else {

  }
}

function deleteTaskZHD(docProcess, doc) {
  var sql = "SELECT * FROM tlk_gy_st_03_assemblylist WHERE PARENT = '" + doc.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var data = iter.next();
      docProcess.doRemove(data);
    }
  } else {

  }
}

//校验按组采购的SKU是否在普源采购订单中有登录组装任务
function ValidCGOrderMakeZHD(doc) {
  var sql = "SELECT * FROM tlk_gy_cg_01_order_detail WHERE PARENT = '" + doc.getId() + "' " +
    " AND ITEM_成品供应商 not in ('', '广州仓', '北海仓', '河北仓')";
  var arr = MagicQueryBySql("magic", sql);
  var skus = "";
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var docItem = arr.get(j);
      //按组采购的成品SKU
      var skuPart = getOrderItemsParentSKU(docItem);
      if (isNotNull(skuPart)) {
        if (isNotNull(skus))
          skus += ",";
        skus += skuPart;
      }
    }
  }
  if (isNotNull(skus) == false)
    return "";
  var billNumber = doc.getItemValueAsString("采购申请单号");
  var result = LoadAssemblyTask(billNumber, skus);
  return result;
}

function getOrderItemsParentSKU(docItem) {
  //获取采购订单中的SKU（可能是物料)
  var value = docItem.get("ITEM_备注"); "来源Fo1AF_Q250需求1套共计250个,来源Fo1AF_Q250需求15套共计3750个";
  const regex = /[A-Za-z0-9]+_[A-Za-z0-9]+/g; // 匹配字母数字+下划线+字母数字
  const arr = value.match(regex);
  var skus = "";
  if (arr != null && arr.length > 0) {
    for (var j = 0; j < arr.length; j++) {
      var data = arr[j];
      if (isNotNull(skus))
        skus += ",";
      skus += "'" + data + "'";

    }
  }
  if (isNotNull(skus) == false) {
    //如果没有务求信息，这个SKU就是成品SKU
    skus = "'" + docItem.get("ITEM_SKU") + "'";
  }
  return skus;
}

function LoadAssemblyTask(billNumber, skus) {
  //billNumber为采购申请单编号
  //skus为按组采购的SKU
  //筛选出按组采购SKU组装任务，并且逐一校验在普源订单中的备注有没有登记相关的组装任务编号
  //获取采购申请单成品表数据
  var sql = "select tam.ITEM_编号, tam.ITEM_成品SKU, tam.ITEM_组装仓库 from tlk_gy_cg_01_purchase_main tm " +
    "left join tlk_gy_cg_01_purchase_detail_combo td on tm.ID = td.PARENT " +
    "left join tlk_gy_st_03_assemblytask tam on " +
    "tm.ITEM_编号 = tam.ITEM_来源单号 and tam.ITEM_成品SKU = td.ITEM_SKU " +
    "where tm.ITEM_编号 = '" + billNumber + "' and td.ITEM_SKU in (" + skus + ")";
  debug(sql);
  var arr = MagicQueryBySql("magic", sql);
  debug("arr.size=" + arr.size());
  var result = "";
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var data = arr.get(j);
      var part = ExecuteValidZHD(data.get("ITEM_编号"),
        data.get("ITEM_成品SKU"), data.get("ITEM_组装仓库"));
      if (isNotNull(part)) {
        if (isNotNull(result))
          result += "、";
        result += part;
      }
    }
  }
  return result;
}

function ExecuteValidZHD(taskNumber, sku, storeName) {
  storeName = TransStoreName(storeName);
  debug("ExecuteValidZHD.billNumber=" + taskNumber + " sku=" + sku + " storeName=" + storeName);
  //普源整合单里是否存在组装任务的备注，如果有校验是否存在SKU
  var sql = "select * from SHOPELF.CG_STOCKORDERM tm " +
    "left join SHOPELF.CG_STOCKORDERD td on tm.NID = td.StockOrderNID " +
    "left join SHOPELF.B_Goods bg on td.GoodsID = bg.NID " +
    "left join SHOPELF.B_Store bs on tm.StoreID = bs.NID " +
    "where tm.memo LIKE '%" + taskNumber + "%' and  bs.StoreName = '" + storeName + "'";
  debug(sql);
  const data = MagicFindBySql("DATACENTER_SHOPELF", sql);
  if (data == null)
    return taskNumber; // + "." + storeName + "中的" + sku
  return "";
}

//到货或归档结束采购跟踪流程
function CloseTrackingBill() {
  var sql = "SELECT distinct ID, ITEM_普源采购订单编号 FROM tlk_gypt_01_main WHERE StateLabel = '等待到货'";
  var arr = MagicQueryBySql("magic", sql);
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var data = arr.get(j);
      var doc = findDocument(data.get("ID"));

      if (CheckBillState(data.get("ITEM_普源采购订单编号"))) {
        debug("CloseTrackingBill test 1");
        if (isNotNull(doc)) {
          debug("CloseTrackingBill test 2");
          //提交到结束
          SubmitFlow(doc, "智能助手", "等待到货", ["结束"], "商品已完全入库，流程自动结束");
        }
      } else {
        if (isNotNull(doc)) {
          //多入库超过订单金额20%或超过200元
          var flag = false;
          if (doc.getItemValueAsDouble("未入库金额") < 0) {
            flag = ABS(doc.getItemValueAsDouble("未入库金额")) / doc.getItemValueAsDouble("采购金额合计") > 0.2 ||
              ABS(doc.getItemValueAsDouble("未入库金额")) > 200;
          }
          if (doc.getItemValueAsDouble("未入库金额") <= 0 && flag == false) {
            SubmitFlow(doc, "智能助手", "等待到货", ["结束"], "商品已完全入库，流程自动结束");
          }
        }
      }
    }
  }
}

function CheckBillState(billNumber) {
  var sql = "select ttm.BillNumber, ttm.CheckFlag, ttm.Archive, ttm.OrderMoney, ttsid.InMoney from " +
    "(select tm.BillNumber, tm.CheckFlag, tm.Archive, SUM(td.AllMoney) as OrderMoney from SHOPELF.CG_STOCKORDERM tm   " +
    "  left join SHOPELF.CG_STOCKORDERD td on tm.NID = td.StockOrderNID " +
    "  where tm.CheckFlag = 1 " +
    "  group by tm.BillNumber, tm.CheckFlag, tm.Archive) ttm " +
    "  left join   " +
    "  (select tsim.StockOrder, SUM(tsid.AllMoney) as InMoney from SHOPELF.CG_StockInM tsim " +
    "      left join SHOPELF.CG_STOCKIND tsid on tsim.NID = tsid.StockInNID " +
    "      where tsim.CheckFlag = 1 " +
    "      group by tsim.StockOrder) ttsid " +
    "  on ttm.BillNumber = ttsid.StockOrder " +
    "  where ttm.BillNumber = '" + billNumber + "'  ";
  //CheckFlag 0    未审核
  //CheckFlag 1    审核
  //CheckFlag 3    作废
  //Archive   1  归档
  //debug("CheckBillState.sql=" + sql);
  var data = MagicFindBySql("DATACENTER_SHOPELF", sql);
  if (isNotNull(data)) {
    //debug("CheckBillState.CheckFlag=" + data.get("CheckFlag"));
    if (data.get("CheckFlag") == 3 || data.get("Archive") == 1)
      return true;
    return data.get("OrderMoney") <= data.get("InMoney")
  } else
    return false;
}

//对已处理的采购跟踪单自动提交到等待到货
function SubmitToWaitTrackingBill() {
  var sql = "SELECT distinct ID, ITEM_普源采购订单编号 FROM tlk_gypt_01_main WHERE StateLabel = '登记采购跟踪情况'";
  var arr = MagicQueryBySql("magic", sql);
  var docProcess = getDocProcess(SysApplicationID());
  if (arr != null && arr.size() > 0) {
    for (var j = 0; j < arr.size(); j++) {
      var data = arr.get(j);
      var doc = findDocument(data.get("ID"));
      //更新数据
      UpdateOACGMsg(doc);
      docProcess.doUpdate(doc);
      var recipient = doc.getAuditorNames();
      var notInMoney = doc.getItemValueAsDouble("未入库金额");
      if (notInMoney == 0) {
        SubmitFlow(doc, recipient, "登记采购跟踪情况", ["等待到货"], "已到货自动提交。");
        continue;
      }
      if (TrackingBillIsAccessed(doc)) {
        SubmitFlow(doc, recipient, "登记采购跟踪情况", ["等待到货"], "系统检查采购员已处理采购跟踪，自动提交到等待到货 。");
      }
    }
  }
}


function TrackingBillIsAccessed(doc) {
  var billNumber = doc.getItemValueAsString("普源采购订单编号")
  var deliveDate = doc.getItemValueAsDate("最新预计到货日期");
  var makeStr = billNumber.substring(4, 14);
  var makeDate = parseDate(makeStr, "yyyy-MM-dd");

  //超期
  if (diffDays(deliveDate, getToday()) > 0) {
    return getLastAccesseDateByNote(doc);
  }
  //是否已处理，当没有出现在待办查询中的记录，就认为是已处理。
  var sql = "SELECT * FROM voa.view_gy_pt_01_purchase_initiation_tracking ";
  sql += " WHERE BillNumber = '" + billNumber + "' ";
  var arr = MagicQueryBySql("DATACENTER_SHOPELF", sql);
  if (arr == null || arr.size() < 1)
    return true;
  //货期：下单日期~预计发货日期的天数
  var days = diffDays(makeDate, deliveDate);
  //最后一次处理时间
  var lastAccessDate = getLastAccesseDateByNote(doc);
  //1.货期60天以上订单，下单后30天开始，每隔20天至少要跟进一次
  if (days >= 60 && diffDays(makeDate, getToday()) >= 30 &&
    diffDays(lastAccessDate, getToday()) > 20)
    return false;
  //2.货期40~60天订单，下单后25天开始，每隔15天至少要跟进一次
  if (days >= 40 && days < 60 && diffDays(makeDate, getToday()) >= 25 &&
    diffDays(lastAccessDate, getToday()) > 15)
    return false;
  //3.货期20~40天订单，下单后15天，每隔10天至少要跟进一次
  debug("billNumber=" + billNumber + ", makeDate=" + makeDate + ", lastAccessDate = " + lastAccessDate);
  if (days >= 20 && days < 40 && diffDays(makeDate, getToday()) >= 15 &&
    diffDays(lastAccessDate, getToday()) > 15)
    return false;
  //4.货期10~20天订单，下单后10天，每隔5天至少跟进一次
  if (days >= 10 && days < 20 && diffDays(makeDate, getToday()) >= 10 &&
    diffDays(lastAccessDate, getToday()) > 5)
    return false;
  //5.货期10天以内订单，不定时监控。

  //6.到货日期前5天要填写跟踪单号。
  if (diffDays(getToday(), deliveDate) < 5 && isNotNull(doc.getItemValueAsString("跟踪单号"))) {
    doc.findItem("跟踪类型").setValue("未填写跟踪单号");
    return false;
  }
  //7.超过预计到货日期，需要每7天跟踪一次
  if (diffDays(deliveDate, getToday()) > 0 && diffDays(lastAccessDate, getToday()) > 7) {
    doc.findItem("跟踪类型").setValue("超期订单");
    return false;
  }
  return true;
}


function getLastAccesseDateByNote(doc) {
  var billNumber = doc.getItemValueAsString("普源采购订单编号");
  var billDate = billNumber.substring(4, 14);
  var billDatePart = splitString(billDate, "-");
  var billYear = parseInt(billDatePart[0]);
  var billMonth = parseInt(billDatePart[1]);

  const note = doc.getItemValueAsString("内部便签");
  if (isNotNull(note) == false)
    return parseDate(billDate, "yyyy-MM-dd");
  var noteLines = splitString(note, " ");
  var noteDate = noteLines[1];
  var noteDatePart = splitString(noteDate, "-");
  var noteMonth = parseInt(noteDatePart[0]);
  var noteDates = parseInt(noteDatePart[1]);

  var resultYear = billYear;
  if (billMonth > noteMonth)
    resultYear += 1;
  var lastDate = parseDate(resultYear + "-" + noteMonth + "-" + noteDates, "yyyy-MM-dd");
  return lastDate;
  //var result = diffDays(lastDate, getToday());
  //return result < 7;
}


function IsExistsTrackingTask(billNumber) {
  //判断是否存在跟踪任务
  var sql = "select * from tlk_gypt_01_main where ITEM_普源采购订单编号 = '" + billNumber + "'";
  var data = MagicFindBySql("magic", sql);
  return data != null;
}

function CreateTrackingTask(docProcess, Form, data) {
  //创建跟踪任务
  var user = GetAssistant();
  var params = createParamsTable();
  var doc = docProcess.doNew(Form, user, params);
  doc.addStringItem("填写人", user.getName());
  doc.addDateItem("填写日期", getToday());
  doc.findItem("采购申请单编号").setValue(data.get("ITEM_采购申请单"));
  doc.findItem("采购订单编号").setValue(data.get("ITEM_采购订单编号"));
  doc.findItem("采购付款单编号").setValue(data.get("ITEM_采购付款申请单编号"));
  doc.findItem("普源采购订单编号").setValue(data.get("BillNumber"));
  doc.findItem("采购员").setValue(data.get("ITEM_采购员"));
  doc.findItem("结算方式").setValue(data.get("ITEM_结算方式"));
  doc.findItem("原预计到货日期").setValue(data.get("DelivDate"));
  doc.findItem("最新预计到货日期").setValue(data.get("DelivDate"));
  doc.findItem("累计逾期天数").setValue(diffDays(data.get("DelivDate"), getToday()));
  doc.findItem("本次逾期天数").setValue(diffDays(data.get("DelivDate"), getToday()));

  doc.findItem("SKU").setValue(data.get("SKU"));
  doc.findItem("采购商品数量").setValue(data.get("Amount"));
  doc.findItem("入库商品数量").setValue(data.get("InAmount"));
  doc.findItem("未入库商品数量").setValue(data.get("Amount") - data.get("InAmount"));
  UpdateOACGMsg(doc);
  docProcess.doCreate(doc);//创建                                                       
  var flowid = "__AuEFr0wOVUvaNSk8YBI";
  var billParams = createParamsTable();
  billParams.setParameter("_flowid", flowid);
  docProcess.doStartFlowOrUpdate(doc, billParams, user); //启动流程 
}

function SubmitTrackingTask(docProcess, billNumber) {
  //如果当前节点为等待到货节点， 提交
  var sql = "SELECT * FROM tlk_gypt_01_main where ITEM_普源采购订单编号 = '" + billNumber + "'";
  var data = MagicFindBySql("magic", sql);
  if (isNotNull(data) == false) {
    debug("找不到单号：" + billNumber);
    return;
  }
  var doc = findDocument(data.get("ID"));
  UpdateOACGMsg(doc);
  docProcess.doUpdate(doc);
  var stateLabel = doc.getStateLabel();
  if ("等待到货".equals(stateLabel)) {
    debug("当前审批状态为等待到货，流程提交到采购员。");
    try {
      SubmitFlow(doc, "智能助手", "等待到货", ["登记采购跟踪情况"], "系统自动流转");
    } catch (e) {

    }
  } else {
    debug("当前审批状态为:" + stateLabel + ", 不需要干预。");
  }

}

function UpdateOACGMsg(doc) {
  var purchaseSql = "SELECT * FROM tlk_gy_cg_01_purchase_main where ITEM_编号 = '" + doc.getItemValueAsString("采购申请单编号") + "'";
  var purchaseData = MagicFindBySql("magic", purchaseSql);
  if (isNotNull(purchaseData)) {
    //采购申请单
    doc.findItem("采购类型").setValue(purchaseData.get("ITEM_采购类型"));
    doc.findItem("申请人").setValue(purchaseData.get("ITEM_申请人"));
  }

  var paymentSql = "SELECT * FROM tlk_gy_cg_02_payment where ITEM_编号 = '" + doc.getItemValueAsString("采购付款单编号") + "'";
  //debug("UpdateOACGMsg.paymentSql=" + paymentSql);
  var paymentData = MagicFindBySql("magic", paymentSql);
  if (isNotNull(paymentData)) {
    //采购计款申请单
    doc.findItem("结算方式").setValue(paymentData.get("ITEM_结算方式"));
    doc.findItem("款项类别").setValue(paymentData.get("ITEM_款项类别"));

    var wlfee = parseDouble(paymentData.get("ITEM_物流费"));
    if (isNotNull(wlfee) == false)
      wlfee = 0;
    var fzfee = parseDouble(paymentData.get("ITEM_分装费"));
    if (isNotNull(fzfee) == false)
      fzfee = 0;
    var fee = wlfee + fzfee;
    doc.findItem("费用合计").setValue(fee);

    //获取payment_detail表的合计
    var pdsql = "";
    if ("现结".equals(doc.getItemValueAsString("结算方式"))) {
      //现结
      var paymentDoc = LoadByIndexFieldValue("tlk_gy_cg_02_payment", "ITEM_编号", doc.getItemValueAsString("采购付款单编号"), true);
      if (isNotNull(paymentDoc)) {
        pdsql = "select SUM(ITEM_支付金额) as 支付金额, " +
          "GROUP_CONCAT(DATE_FORMAT(ITEM_付款日期, '%Y-%m-%d'), '支付', round(ITEM_支付金额, 2), '元') as 付款日期  " +
          "from magic5.tlk_gy_cg_02_payment_detail where PARENT = '" + paymentDoc.getId() + "'";
      }
    } else if ("定期结算".equals(doc.getItemValueAsString("结算方式"))) {
      //定期结算
      pdsql = "select SUM(ITEM_实付金额) as 支付金额, GROUP_CONCAT(DATE_FORMAT(LASTMODIFIED, '%Y-%m-%d'), '支付', round(ITEM_实付金额, 2), '元') as 付款日期 " +
        " from tlk_gy_cg_02_payment_by_month_detail_ " +
        "where ITEM_普源采购订单编号 = '" + doc.getItemValueAsString("普源采购订单编号") + "'";
    }
    if (isNotNull(pdsql)) {
      debug(pdsql);
      var pddata = MagicFindBySql("magic", pdsql);
      if (isNotNull(pddata)) {
        doc.findItem("VOA实际付款金额").setValue(pddata.get("支付金额"));
        doc.findItem("付款日期").setValue(pddata.get("付款日期"));
      }
    } else {
      //空，判断不了的
      var paymentDoc = LoadByIndexFieldValue("tlk_gy_cg_02_payment", "ITEM_编号", doc.getItemValueAsString("采购付款单编号"), true);
      if (isNotNull(paymentDoc)) {
        pdsql = "select SUM(ITEM_支付金额) as 支付金额, " +
          "GROUP_CONCAT(ITEM_付款日期, ':', ITEM_支付金额) as 付款日期  " +
          "from magic5.tlk_gy_cg_02_payment_detail where PARENT = '" + paymentDoc.getId() + "'";
      }
      //debug("pdsql=" + pdsql);
      var pddata = MagicFindBySql("magic", pdsql);
      if (isNotNull(pddata)) {
        doc.findItem("VOA实际付款金额").setValue(pddata.get("支付金额"));
        doc.findItem("付款日期").setValue(pddata.get("付款日期"));
      } else {
        //现结没有数据，从定期结算获取数据
        pdsql = "select SUM(ITEM_实付金额) as 支付金额, GROUP_CONCAT(LASTMODIFIED, ':', ITEM_实付金额) as 付款日期 " +
          " from tlk_gy_cg_02_payment_by_month_detail_ " +
          "where ITEM_普源采购订单编号 = '" + doc.getItemValueAsString("普源采购订单编号") + "'";
        pddata = MagicFindBySql("magic", pdsql);
        if (isNotNull(pddata)) {
          doc.findItem("VOA实际付款金额").setValue(pddata.get("支付金额"));
          doc.findItem("付款日期").setValue(pddata.get("付款日期"));
        } else {
          doc.findItem("VOA实际付款金额").setValue(0);

        }
      }
    }
  }

  //采购订单明细信息
  var orderSql = "SELECT * FROM voa.view_gy_pt_01_payment_group WHERE BillNumber = '" + doc.getItemValueAsString("普源采购订单编号") + "'";
  var orderData = MagicFindBySql("DATACENTER_BUSINESS", orderSql);
  if (isNotNull(orderData)) {
    doc.findItem("SKU").setValue(orderData.get("SKU"));
    doc.findItem("采购商品数量").setValue(round(orderData.get("Amount"), 0));
    doc.findItem("入库商品数量").setValue(round(orderData.get("InAmount"), 0));
    doc.findItem("未入库商品数量").setValue(round(orderData.get("Amount") - orderData.get("InAmount"), 0));
  }

  //普源订单信息
  var pyosql = ReadSql("GY_PT_01_Puyuan_CGOrder_Tracking");
  pyosql = "SELECT * FROM (" + pyosql + ") TableA";
  var pyosql = ReplaceAll(pyosql, "BillNumber", doc.getItemValueAsString("普源采购订单编号"));
  var pyodata = MagicFindBySql("DATACENTER_SHOPELF", pyosql);
  if (isNotNull(pyodata) == false)
    return;
  doc.findItem("单据状态").setValue(pyodata.get("单据状态"));
  doc.findItem("采购员").setValue(pyodata.get("采购员"));
  doc.findItem("供应商").setValue(pyodata.get("供应商"));
  doc.findItem("采购仓库").setValue(pyodata.get("采购仓库"));
  doc.findItem("最新预计到货日期").setValue(pyodata.get("预计到货日期"));

  //记录预付到货日期修改记录
  var oldLog = doc.getItemValueAsString("到货日期修改记录");
  var sourceAmount = doc.getItemValueAsDouble("原预计到货日期");
  var adjustAmount = doc.getItemValueAsDouble("最新预计到货日期");
  if ((isNotNull(oldLog) == false && sourceAmount == adjustAmount) == false){
    var newLog = TextInputAddLog(user, doc, "到货日期修改记录", "最新预计到货日期");
    if (oldLog.equals(newLog) == false) {
      doc.findItem("到货日期修改记录").setValue(newLog);
    }
  }

  doc.findItem("付款方式").setValue(pyodata.get("付款方式"));
  var firstDays = doc.getItemValueAsDate("原预计到货日期");
  var lastDays = doc.getItemValueAsDate("最新预计到货日期");
  var totalDelayDays = diffDays(firstDays, getToday());
  if (totalDelayDays < 0)
    totalDelayDays = 0;
  var currentDelayDays = diffDays(lastDays, getToday());
  if (currentDelayDays < 0)
    currentDelayDays = 0;
  doc.findItem("累计逾期天数").setValue(totalDelayDays);
  doc.findItem("本次逾期天数").setValue(currentDelayDays);
  doc.findItem("内部便签").setValue(pyodata.get("内部便签"));
  doc.findItem("采购金额合计").setValue(pyodata.get("采购金额合计"));
  doc.findItem("采购订单金额合计").setValue(pyodata.get("采购订单金额合计"));
  doc.findItem("采购入库金额合计").setValue(pyodata.get("采购入库金额合计"));
  doc.findItem("未入库金额").setValue(pyodata.get("未入库金额"));
  var payNotIn = doc.getItemValueAsDouble("VOA实际付款金额") - doc.getItemValueAsDouble("采购入库金额合计");
  doc.findItem("已付款未入库金额").setValue(payNotIn);
  //更新跟踪类型
  doc.findItem("跟踪类型").setValue("超期订单");

  //超期订单
  var delivDate = doc.getItemValueAsDate("原预计到货日期");
  var daysCount = diffDays(delivDate, getToday());
  if (daysCount <= 7)
    doc.findItem("跟踪类型").setValue("常规跟踪");

  //无交货日期
  if (isNotNull(delivDate) == false)
    doc.findItem("跟踪类型").setValue("无交货日期");

  //更新跟踪类型
  UpdateTrackingKind(doc);
}

function UpdateTrackingKind(doc) {
  debug("UpdateTrackingKind test 1");
  var billNumber = doc.getItemValueAsString("采购付款单编号");
  var sql = "SELECT " +
    "  tm.ITEM_编号, " +
    "  agg.ITEM_付款日期, " +
    "  CASE " +
    "    WHEN agg.max_score = 2 THEN '结清' " +
    "    WHEN agg.max_score = 1 THEN '部份付款' " +
    "    else '未付款' " +
    "  END AS ITEM_付款情况 " +
    "FROM tlk_gy_cg_02_payment tm " +
    "LEFT JOIN ( " +
    "  SELECT " +
    "    td.PARENT, " +
    "    MAX( " +
    "      CASE " +
    "        WHEN TRIM(td.ITEM_款项类别) IN ('全款', '尾款') THEN 2 " +
    "        WHEN TRIM(td.ITEM_款项类别) = '部份尾款' THEN 1 " +
    "        WHEN td.ITEM_款项类别 IS NULL OR TRIM(td.ITEM_款项类别) = '' OR TRIM(td.ITEM_款项类别) = '订金' THEN 0 " +
    "        ELSE -1 " +
    "      END " +
    "    ) AS max_score, " +
    "    Max(td.ITEM_付款日期) as ITEM_付款日期 " +
    "  FROM tlk_gy_cg_02_payment_detail td " +
    "  GROUP BY td.PARENT " +
    ") AS agg " +
    "  ON tm.ID = agg.PARENT " +
    "WHERE tm.ITEM_编号 = '" + billNumber + "'";
  debug(sql);
  var data = MagicFindBySql("magic", sql);
  if (isNotNull(data)) {
    var zfKind = data.get("ITEM_付款情况");
    doc.findItem("付款情况").setValue(zfKind);
    doc.findItem("最近一次付款日期").setValue(data.get("ITEM_付款日期"));
    debug("付款情况：" + zfKind);
    if ("未付款".equals(zfKind)) {
      doc.findItem("已付款商品金额").setValue(0);
    } else if ("部份付款".equals(zfKind)) {
      var skuMoney = PayForSKU(doc);
      doc.findItem("已付款商品金额").setValue(skuMoney);
    } else if ("结清".equals(zfKind)) {
      //洁清
      var orderMoney = CGSKUMoney(doc);
      doc.findItem("已付款商品金额").setValue(orderMoney);
    }
    var skuMoney = doc.getItemValueAsDouble("已付款商品金额");
    var payMoney = doc.getItemValueAsDouble("VOA实际付款金额");
    var inMoney = doc.getItemValueAsDouble("采购入库金额合计");
    debug("skuMoney=" + skuMoney + ", inMoney=" + inMoney);
    if (skuMoney > inMoney) {
      debug("已付款未到货");
      doc.findItem("跟踪类型").setValue("已付款未到货");
    }
  } else {
    if (isNotNull(doc.getItemValueAsString("跟踪类型")) == false)
      doc.findItem("跟踪类型").setValue("常规跟踪");
    doc.findItem("付款情况").setValue("未付款");
    doc.findItem("已付款商品金额").setValue(0);

  }

}

function PayForSKU(doc) {
  var billNumber = doc.getItemValueAsString("采购付款单编号");
  var sql = "select SUM(td.ITEM_金额) as ITEM_Money from tlk_gy_cg_02_payment tm " +
    "left join tlk_gy_cg_02_payment_sku td on tm.ID = td.PARENT " +
    "where tm.ITEM_编号 = '" + billNumber + "'";
  debug("PayForSKU.sql=" + sql);
  var value = sumBySQL(sql, "Money");
  return value;
}

function CGSKUMoney(doc) {
  //普源订单的金额
  var billNumber = doc.getItemValueAsString("普源采购订单编号");
  var sql = "select OrderMoney from SHOPELF.CG_STOCKORDERM where BillNumber = '" + billNumber + "'";
  debug("CGSKUMoney.sql=" + sql);
  var data = MagicFindBySql("DATACENTER_SHOPELF", sql);
  if (isNotNull(data) == false)
    return 0;
  else {
    return data.get("OrderMoney");
  }
}
```

---

## 3. SalesUtils

**模块说明：** 销售和售后服务相关的函数集合，主要处理各平台售后工单的自动生成、售后率统计、新品上架等销售业务流程。

**主要功能：**

- 亚马逊售后工单自动生成
- 普源售后工单处理
- 售后率统计和监控
- 新品上架流程管理
- 平台售后数据分析
- 工单状态跟踪

**数据来源：**

- DATACENTER_SHOPELF: 商品和售后数据
- DATACENTER_BUSINESS: 业务统计数据
- magic: 系统业务数据

**使用场景：**

- 定时检查各平台售后率异常
- 自动创建售后处理工单
- 新品入库后自动发起上架流程
- 售后数据统计和分析
- 工单流程自动化处理

**注意事项：**

- 工单创建前需检查是否已存在相同工单
- 售后率计算基于30天周期数据
- 平台名称需要标准化转换
- 工单分配需要获取对应平台负责人
- 定时任务执行频率需要合理控制

**主要函数：**

```javascript
/
/**
 * 获取亚马逊售后工单
 * 根据售后率数据自动创建亚马逊售后工单
 */
function Job_Amazon_Aftermarket_WorkOrder(){
  var kind = "亚马逊售后率";
  var date = adjustDay(getToday(), -30);
  var dateStr = format(date,"yyyyMM");
  var domainid = "ZbcBRS8hXO7lfuoz6mP";
  var sql = ReadSql("Job_Amazon_Aftermarket_WorkOrder");
  sql = sql.replace("{DomainID}", domainid);
  var applicationId = "__J0juyJ97xxO8Lvezg4P";
  var docProcess = getDocProcess(applicationId);
  var arr = MagicQueryBySql("DATACENTER_SHOPELF", sql);
  if (arr != null && arr.size() > 0){
    for(var j =0; j < arr.size(); j++){
      var vo = arr.get(j);
      var userName = vo.get("DIRECTOR");
      var userLoginNo = getUserLoginnoByUserName(userName);
      var user = getUserByLoginno(userLoginNo);
      if (!user)
        user = GetLeader(vo.get("PLATFORM"));
      var sku = vo.get("SKU");
      if (IsAlreadySendForAmazon(vo, kind)){
        Packages.java.lang.System.out.println("当前商品最近已发起过工单。**********************************************");
        continue;
      }
      //创建售后单                              
      var cgdPrefix="YWSHGD-";                                
      var cgBillNo = countNext2(cgdPrefix,true,true,true,4);                               
      var billFormName = "YWSH_02_AfterSales_WorkOrders_main";                              
      var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();                              
      var Form = formProcess.doViewByFormName(billFormName, applicationId);                            
      var params = createParamsTable();                              
      mainDoc = docProcess.doNew(Form, user, params);                              
      mainDoc.addStringItem("编号",cgBillNo);                              
      mainDoc.addStringItem("填写人", user.getName());                              
      mainDoc.addStringItem("工单类型", kind);    
      mainDoc.addDateItem("填写日期",getToday());                              
      mainDoc.addStringItem("反馈平台", vo.get("PLATFORM"));                              
      mainDoc.addStringItem("运营账号", vo.get("ACCOUNT"));
      mainDoc.addStringItem("区域", vo.get("AREA"));
      mainDoc.addStringItem("SHL_SKU", vo.get("SKU_SERIES"));
      mainDoc.addDoubleItem("SHL_周期总售出量", vo.get("ORDERS"));
      mainDoc.addDoubleItem("SHL_售后单量", vo.get("AFTERSALE_ORDERS"));
      mainDoc.addDoubleItem("SHL_总售后率", vo.get("AFTERSALE_RATE"));
      mainDoc.addDoubleItem("SHL_统计周期", vo.get("Y_M").replace(".0",""));
      docProcess.doCreate(mainDoc);//创建
      //重新获取售后工单对象                              
      var orderMainDoc = LoadByIndexFieldValue("tlk_ywsh_02_aftersales_workorders_main", "ITEM_编号", cgBillNo, true); 
     //发起流程
      var flowid = "__jRMerd8ElQHoOZiAazP";
      var billParams = createParamsTable();
      billParams.setParameter("_flowid", flowid);
      docProcess.doStartFlowOrUpdate(orderMainDoc, billParams, user); //启动流程 
      //通知
      SendQQMessageByDoc(orderMainDoc, "售后工单：" + kind, "通知", "请您尽快处理。");  
    }
  }
}

/**
 * 检查亚马逊售后工单是否已经发送
 * @param {Object} vo - 售后数据对象
 * @param {string} kind - 工单类型
 * @returns {boolean} true表示已发送
 */
function IsAlreadySendForAmazon(vo, kind){
  var sql = "SELECT * FROM tlk_ywsh_02_aftersales_workorders_main WHERE 1 = 1 " +
      "AND ITEM_填写人 = '" + vo.get("DIRECTOR") + "' " +
      "AND ITEM_工单类型 = '" + kind + "' " +
      "AND ITEM_反馈平台 = '" + vo.get("PLATFORM") + "' " +
      "AND ITEM_运营账号 = '" + vo.get("ACCOUNT") + "' " +
      "AND ITEM_SHL_SKU = '" + vo.get("SKU_SERIES") + "' " +
      "AND ITEM_SHL_统计周期 = '" + vo.get("Y_M").replace(".0","") + "'";
  var t_count = countBySQL(sql);
  return t_count > 0;
}

/**
 * 获取普源售后工单
 * 根据普源系统数据自动创建售后工单
 */
function Job_Puyuan_Aftermarket_WorkOrder(){
  var kind = "普源售后";
  var date = adjustDay(getToday(), -30);
  var dateStr = format(date,"yyyyMM");
  var domainid = "ZbcBRS8hXO7lfuoz6mP";
  var sql = ReadSql("PuYuanAfterSalesServiceOrder");
  sql = sql.replace("{DomainID}", domainid);
  println("sql=" + sql);
  var arr = MagicQueryBySql("DATACENTER_BUSINESS", sql); 
  var applicationId = getApplication();
  var docProcess = getDocProcess(applicationId);
  var billFormName = "YWSH_02_AfterSales_WorkOrders_main";                              
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl(); 
  var Form = formProcess.doViewByFormName(billFormName, applicationId);  
  if (arr != null && arr.size() > 0){ 
    for(var j =0; j < arr.size(); j++){
      var vo = arr.get(j);
      var subKind = vo.get("Kind");
      if (IsAlreadySendForPuyuan(vo, kind, subKind)){
        continue;
      }
      AddPuyuanWorkOrder(docProcess, formProcess, Form, vo, kind, subKind);
    }
  }
}

/**
 * 获取运营部各组组长
 * @param {string} platform - 运营平台名称
 * @returns {Object} 组长用户对象
 */
function GetLeader(platform){
  var leader = GetPlatformManager(platform);
  if (leader != null)
    return leader;
  var userName = DefaultPlatformManager();
  var userLoginNo = getUserLoginnoByUserName(userName);
  var user = getUserByLoginno(userLoginNo);
  return user;
}

/**
 * 添加普源售后工单
 * @param {Object} docProcess - 文档处理对象
 * @param {Object} formProcess - 表单处理对象
 * @param {Object} Form - 表单对象
 * @param {Object} vo - 售后数据对象
 * @param {string} kind - 工单类型
 * @param {string} SubKind - 子类型
 */
function AddPuyuanWorkOrder(docProcess, formProcess, Form, vo, kind, SubKind){
  var userLoginNo = getUserLoginnoByUserName(vo.get("DIRECTOR"));
  var user = getUserByLoginno(userLoginNo);
  if (!user)
    user = GetLeader(vo.get("PLATFORM"));
  var sku = vo.get("SKU");
  var platform = TransPlatformName(vo.get("PLATFORM"));
  if (isNotNull(platform) == false)
    return;
  //创建售后单                              
  var cgdPrefix="YWSHGD-";                                
  var cgBillNo = countNext2(cgdPrefix,true,true,true,4);                                                     
  var params = createParamsTable();           
  var mainDoc = docProcess.doNew(Form, user, params);                              
  mainDoc.addStringItem("编号",cgBillNo);                              
  mainDoc.addStringItem("填写人", vo.get("DIRECTOR"));                              
  mainDoc.addStringItem("工单类型", kind);
  mainDoc.addStringItem("PYSH_售后类型", SubKind);
  mainDoc.addDateItem("填写日期",getToday());                              
  mainDoc.addStringItem("反馈平台", platform);                              
  mainDoc.addStringItem("运营账号", vo.get("Account"));
  mainDoc.addStringItem("PYSH_订单编号", vo.get("OrderNumber"));
  mainDoc.addStringItem("PYSH_售后单号", vo.get("BillNumber"));
  mainDoc.addStringItem("PYSH_售后内容", vo.get("Memo"));
  mainDoc.addStringItem("PYSH_原因", vo.get("Reason"));
  mainDoc.addStringItem("PYSH_订单交易日期", vo.get("MakeDate"));
  mainDoc.addStringItem("PYSH_币种", vo.get("CurrencyCode"));
  mainDoc.addDoubleItem("PYSH_退款金额", vo.get("ReMoney"));
  mainDoc.addDoubleItem("PYSH_订单金额", vo.get("Amt"));
  mainDoc.addStringItem("PYSH_包裹重量", vo.get("TotalWeight"));
  docProcess.doCreate(mainDoc);//创建
  //重新获取售后工单对象                              
  var orderMainDoc = LoadByIndexFieldValue("tlk_ywsh_02_aftersales_workorders_main", "ITEM_编号", cgBillNo, true); 
  //发起流程
  var flowid = "__jRMerd8ElQHoOZiAazP";
  var billParams = createParamsTable();
  billParams.setParameter("_flowid", flowid);
  docProcess.doStartFlowOrUpdate(orderMainDoc, billParams, user); //启动流程 
  //通知
  SendQQMessageByDoc(orderMainDoc, "售后工单：" + kind, "通知", "请您尽快处理。"); 
}

/**
 * 检查普源售后工单是否已经发送
 * @param {Object} vo - 售后数据对象
 * @param {string} kind - 工单类型
 * @param {string} SubKind - 子类型
 * @returns {boolean} true表示已发送
 */
function IsAlreadySendForPuyuan(vo, kind, SubKind){
  var sql = "SELECT * FROM tlk_ywsh_02_aftersales_workorders_main WHERE 1 = 1 " +
      "AND ITEM_PYSH_订单编号 = '" + vo.get("OrderNumber") + "' " +
      "AND ITEM_工单类型 = '" + kind + "' " +
      "AND ITEM_PYSH_售后类型 = '" + SubKind + "' ";
  println("IsAlreadySendForPuyuan.sql=" + sql);
  var data = MagicFindBySql("magic", sql);
  return isNotNull(data);
}

//普源售后率
function PuyuanAfterSale(){
  Packages.java.lang.System.out.println("PuyuanAfterSale1");
  var kind = "普源售后率";
  var date = adjustDay(getToday(), -30);
  var dateStr = format(date,"yyyyMM");
  var domainid = "ZbcBRS8hXO7lfuoz6mP";
  var sql = ReadSql("PuyuanAfterSaleRate");
  sql = sql.replace("{DomainID}", domainid);
  Packages.java.lang.System.out.println("PuyuanAfterSale:" + sql);
  var arr = MagicQueryBySql("DATACENTER_BUSINESS", sql); 
  var applicationId = getApplication();
  var docProcess = getDocProcess(applicationId);
  var billFormName = "YWSH_02_AfterSales_WorkOrders_main";                              
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl(); 
  var Form = formProcess.doViewByFormName(billFormName, applicationId);  
  if (arr != null && arr.size() > 0){ 
    Packages.java.lang.System.out.println("PuyuanAfterSale arr.size=" + arr.size());
    for(var j =0; j < arr.size(); j++){
      var vo = arr.get(j);
      Packages.java.lang.System.out.println("发现普源工单：" + vo.get("OrderNumber") + ", " + vo.get("DIRECTOR"));
      if (IsAlreadySendForPuyuanForAferSaleRate(vo, kind)){
        Packages.java.lang.System.out.println("当前商品最近已发起过工单。");
        continue;
      }
      AddPuyuanWorkOrder_ForOther(docProcess, formProcess, Form, vo, kind);
    }
  }
}

function AddPuyuanWorkOrder_ForOther(docProcess, formProcess, Form, vo, kind){
  var userLoginNo = getUserLoginnoByUserName(vo.get("DIRECTOR"));
  println("userLoginNo = " + userLoginNo);
  var user = getUserByLoginno(userLoginNo);
  if (!user)
    user = GetLeader(vo.get("PLATFORM"));
  var sku = vo.get("SKU_SERIES");
  //创建售后单                              
  var cgdPrefix="YWSHGD-";                                
  var cgBillNo = countNext2(cgdPrefix,true,true,true,4);                                                     
  var params = createParamsTable();           
  var mainDoc = docProcess.doNew(Form, user, params);                              
  mainDoc.addStringItem("编号",cgBillNo);                              
  mainDoc.addStringItem("填写人", vo.get("DIRECTOR"));                              
  mainDoc.addStringItem("工单类型", kind);
  mainDoc.addDateItem("填写日期",getToday());                              
  mainDoc.addStringItem("反馈平台", vo.get("PLATFORM"));                              
  mainDoc.addStringItem("运营账号", vo.get("ACCOUNT"));
  mainDoc.addStringItem("SHL_SKU", vo.get("SKU_SERIES"));
  mainDoc.addDoubleItem("SHL_周期总售出量", vo.get("ORDERS"));
  mainDoc.addDoubleItem("SHL_售后单量", vo.get("AFTERSALE_ORDERS"));
  mainDoc.addDoubleItem("SHL_总售后率", vo.get("AFTERSALE_RATE"));
  mainDoc.addDoubleItem("SHL_统计周期", vo.get("Y_M").replace(".0",""));
  docProcess.doCreate(mainDoc);//创建
  //重新获取售后工单对象
  var orderMainDoc = LoadByIndexFieldValue("tlk_ywsh_02_aftersales_workorders_main", "ITEM_编号", cgBillNo, true); 
  //发起流程
  var flowid = "__jRMerd8ElQHoOZiAazP";
  var billParams = createParamsTable();
  billParams.setParameter("_flowid", flowid);
  Packages.java.lang.System.out.println("PuyuanAfterSale4");
  docProcess.doStartFlowOrUpdate(orderMainDoc, billParams, user); //启动流程 
  Packages.java.lang.System.out.println("PuyuanAfterSale5");
  //通知
  //SendQQMessageByDoc(orderMainDoc, "售后工单：" + kind, "通知", "请您尽快处理。"); 
}

function IsAlreadySendForPuyuanForAferSaleRate(vo){
  var sql = "SELECT * FROM tlk_ywsh_02_aftersales_workorders_main WHERE 1 = 1 " +
      "AND ITEM_反馈平台 = '" + vo.get("PLATFORM") + "' " +
      "AND ITEM_运营账号 = '" + vo.get("ACCOUNT") + "' " +
      "AND ITEM_SHL_统计周期 = '" + vo.get("Y_M").replace(".0","") + "' " +
      "AND ITEM_SHL_SKU = '" + vo.get("SKU_SERIES") + "' ";
  debug("IsAlreadySendForPuyuanForAferSaleRate.sql = " + sql);
  var arr = MagicQueryBySql("magic", sql);
  return arr != null && arr.size() > 0;
}

function TransPlatformName(value){
  if ("EB".equals(value))
    return "eBay";
  else if ("SMT".equals(value) || "速卖通".equals(value))
    return "速卖通";
  else if ("1688".equals(value))
    return "1688";
  else if (value.indexOf("AZ") == 0)
    return "亚马逊";
  else if ("Shopify".equals(value))
    return "独立站";
  else if ("Tiktok".equals(value))
    return "Tiktok";
  else
    return "";
}

//对售后工单中必须的字段进行检验，如果没有填写，返回填写提示词
function validFieldEmpty(doc, vFieldName, clKind, clPrefix,stateLabel){
  //return "";
  var cl = clPrefix;
  var fieldName = vFieldName;
  var op = clKind;
  var st = stateLabel;
  var doc = getCurrentDocument();
  var stateLabel = doc.getStateLabel();
  var value = doc.getItemValueAsString(cl + fieldName);
  var sclKind = doc.getItemValueAsString("BMFZR_处理方式");
  var wtKind = doc.getItemValueAsString("BMFZR_问题类型");
  //println("sclKind=" + sclKind + ", op=" + op);
  //println("stateLabel=" + stateLabel + ", st=" + st);
  //println("value=" + value); 

  if (sclKind.indexOf(op) >= 0 && stateLabel.indexOf(st) >= 0 && isNotNull(value) == false){
    return op + "." + fieldName + " 必须填写";
  } else {

  }
}

//对售后工单中必须的字段(条件字段选中时)进行检验，如果没有填写，返回填写提示词
function validFieldEmptyWithCondition(doc, vFieldName, clKind, clPrefix,stateLabel,
                                     conditionFieldName, conditionValue){
  var cl = clPrefix;
  var fieldName = vFieldName;
  var op = clKind;
  var st = stateLabel;
  var doc = getCurrentDocument();
  var stateLabel = doc.getStateLabel();
  var value = doc.getItemValueAsString(cl + fieldName);
  var isError = doc.getItemValueAsString(cl + conditionFieldName);
  var clKind = doc.getItemValueAsString("BMFZR_处理方式");
  var wtKind = doc.getItemValueAsString("BMFZR_问题类型");
  if (conditionValue.equals(isError) && clKind.indexOf(op) >= 0 && 
      stateLabel.indexOf(st) >= 0 && isNotNull(value) == false){
    //println("clKind=" + clKind + ", op=" + op);
    //println("value=" + value);
    return clKind + "." + fieldName + " 必须填写";
  }
}

//新品首采到货后发起新品入库流程
function JobGenerateArrivalNotice(){
    var sql = ReadSql("Job新品入库通知");
    sql = "SELECT DISTINCT ITEM_编号 FROM (" + sql + ") AS TableA ";
    var arr = MagicQueryBySql("DATACENTER_SHOPELF", sql);
    if (arr != null && arr.size() > 0){
        for (var j = 0; j < arr.size(); j++) {
            var vo = arr.get(j);
            CreateNewArrivalNotice(vo.get("ITEM_编号"));
        }
    }
}

function CreateNewArrivalNotice(billNumber) {
    //判断是否已创建新品入库通知
    var countSql = "select * from tlk_ywxp_02_new_arrivals where ITEM_采购申请单编号 = '" + billNumber + "'";
    var existsData = MagicFindBySql("magic", countSql);
    if (isNotNull(existsData)){
        return;
    }
    var sql = ReadSql("Job新品入库通知");
    sql = "SELECT * FROM (" + sql + ") AS TableA WHERE ITEM_编号 = '" + billNumber + "'";
    var arr = MagicQueryBySql("DATACENTER_SHOPELF", sql);
    if (arr != null && arr.size() > 0) {
        debug("查出来记录数=" + arr.size());
        var applicationId = getApplication();//获取applicationId                            
        var docProcess = getDocProcess(applicationId);
        var billFormName = "YWXP_02_New_Arrivals";
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var Form = formProcess.doViewByFormName(billFormName, applicationId);

        for (var j = 0; j < arr.size(); j++) {
            var vo = arr.get(j);
            var userName = vo.get("ITEM_申请人");
            var userLoginNo = getUserLoginnoByUserName(userName);
            var user = getUserByLoginno(userLoginNo);
            if (user == null)
                continue;
            //创建表单                       
            var cgdPrefix = "YWXPNA-";
            var billNo = countNext2(cgdPrefix, true, true, true, 4);
            var params = createParamsTable();
            debug("准备创建");
            var doc = docProcess.doNew(Form, user, params);
            doc.addStringItem("编号", billNo);
            doc.addStringItem("填写人", user.getName());
            doc.addDateItem("填写日期", getToday());
            doc.addStringItem("采购申请单编号", vo.get("ITEM_编号"));
            doc.addStringItem("采购类型", vo.get("ITEM_采购类型"));
            doc.addDateItem("采购申请单申请时间", vo.get("ITEM_申请日期"));
            doc.addStringItem("采购申请人", vo.get("ITEM_申请人"));
            var ss = vo.get("ITEM_SKU").substring(0, 4);
            doc.addStringItem("SKU系列", ss);
            doc.addStringItem("新品负责人", vo.get("ITEM_申请人"));
            doc.addStringItem("普源采购入库单编号", vo.get("BillNumber"));
            doc.addStringItem("采购仓库", vo.get("StoreName"));
            doc.addDateItem("入库时间", vo.get("MakeDate"));
            doc.addDoubleItem("入库数量", vo.get("Amount"));
            doc.addStringItem("普源采购入库单审批状态", vo.get("Audier"));
            doc.addStringItem("新品上架方式", "新建");
            docProcess.doCreate(doc);
            debug("创建完成");
            //重新获取采购订单对象                            
            doc = LoadByIndexFieldValue("tlk_ywxp_02_new_arrivals", "ITEM_编号", billNo, true);
            var flowid = "__BEG6Et1mzimsbwqi1QH";
            var billParams = createParamsTable();
            billParams.setParameter("_flowid", flowid);
            docProcess.doStartFlowOrUpdate(doc, billParams, user); //启动流程 
            break; //test
        }
    }
}
```

---

## 4. FlowUtils

**模块说明：** 与流程（Flow）操作相关的通用函数集合，包含提交、干预、回退、重启、查节点和定时批量提交等。

**主要功能：**

- 流程提交、干预、回退操作
- 流程节点查询和管理
- 用户权限验证
- 定时批量处理
- 流程状态监控

**使用场景：**

- 自动化流程处理
- 流程异常干预
- 批量流程操作
- 流程状态查询

**注意事项：**

- 流程操作需要相应权限
- 干预操作需要谨慎使用
- 批量操作前建议先测试
- 流程ID和节点ID必须准确

**主要函数：**

```javascript
/**
 * 检查当前用户是否为流程处理人
 * @param {Object} doc - 文档对象
 * @param {Object} user - 用户对象
 * @returns {boolean} true表示是处理人
 */
function IsAccessor(doc, user) {
    if (isNotNull(doc.getId()) == false)
        return true; //新建的单，还没保存。
    var value = doc.getAuditorNames();
    var stateLabel = doc.getStateLabel();
    if (isNotNull(stateLabel) == false && isNotNull(value) == false)
        return true;
    return value.indexOf(user.getName()) >= 0;
}

/**
 * 流程提交操作
 * 使用示例：SubmitFlow(doc, "张三", "第二步", ["第三步"], "系统自动流转");
 * @param {Object} doc - 文档对象
 * @param {string} userName - 操作用户姓名
 * @param {string} curNodeLabel - 当前节点名称
 * @param {Array} nextNodeLabels - 下一节点名称数组
 * @param {string} comment - 提交意见
 */
function SubmitFlow(doc, userName, curNodeLabel, nextNodeLabels, comment) {
    var userId = getUserIdByUserName(userName);
    var user = getUserById(userId);         //当前用户对象为审批人
    var flowID = "";
    if (doc.getState() == null) {
        flowID = doc.getForm().getOnActionFlow();
    } else {
        flowID = doc.getState().getFlowid();
    }
    debug("SubmitFlow.BillNumber=" + doc.getItemValueAsString("编号"));
    var curNodeId = GetCurrentNodeID(doc, curNodeLabel);
    if (isNotNull(curNodeId) == false) {
        debug(doc.getItemValueAsString("编号") + ":SubmitFlow找不到" + curNodeLabel + "节点");
        return;
    }
    var nextNodeIds = GetNextNodeIds(flowID, nextNodeLabels);
    if (nextNodeIds.length < 1) {
        debug("SubmitFlow找不到" + nextNodeLabels + "节点");
        return;
    }
    var applicationId = SysApplicationID();//获取applicationId
    var docProcess = getDocProcess(applicationId);
    var params = createParamsTable();
    params.setParameter("_flowid", flowID);
    var flowOption = "80";   //80提交 86干预、81回退
    FLOW.submitFlow(flowID, curNodeId, nextNodeIds, comment, user, doc);
    debug("流程提交：从" + curNodeLabel + "到" + nextNodeLabels);
}

/**
 * 流程干预操作（管理员权限）
 * 使用示例：InterveneFlow(doc, "管理员", "第二步", ["第三步"], "系统自动干预");
 * @param {Object} doc - 文档对象
 * @param {string} userName - 操作用户姓名
 * @param {string} curNodeLabel - 当前节点名称
 * @param {Array} nextNodeLabels - 下一节点名称数组
 * @param {string} comment - 干预原因
 */
function InterveneFlow(doc, userName, curNodeLabel, nextNodeLabels, comment) {
    debug("SubmitFlow test 1");
    var userId = getUserIdByUserName(userName);
    debug("SubmitFlow test 2");
    var user = getUserById(userId);         //当前用户对象为审批人
    debug("SubmitFlow test 3");
    var flowID = "";
    if (doc.getState() == null) {
        flowID = doc.getForm().getOnActionFlow();
    } else {
        flowID = doc.getState().getFlowid();
    }
    debug("SubmitFlow test 3.1");
    var curNodeId = GetCurrentNodeID(doc, curNodeLabel);
    debug("SubmitFlow test 3.2");
    if (isNotNull(curNodeId) == false) {
        debug("SubmitFlow找不到" + curNodeLabel + "节点");
        return;
    }
    debug("SubmitFlow test 4");
    var nextNodeIds = GetNextNodeIds(flowID, nextNodeLabels);
    debug("SubmitFlow test 5");
    if (nextNodeIds.length < 1) {
        debug("SubmitFlow找不到" + nextNodeLabels + "节点");
        return;
    }
    debug("SubmitFlow test 6");
    var applicationId = SysApplicationID();//获取applicationId
    debug("SubmitFlow test 7");
    var docProcess = getDocProcess(applicationId);
    debug("SubmitFlow test 8");
    var params = createParamsTable();
    params.setParameter("_flowid", flowID);
    var flowOption = "86";   //80提交 86干预、81回退
    if (!docProcess)
        println("docProcess is null");
    if (!doc) {
        println("doc is null");
        return;
    }
    if (!params)
        println("params is null");
    if (!curNodeId) {
        println("curNodeId is null");
        return;
    }
    if (!nextNodeIds) {
        println("nextNodeIds is null");
        return;
    }
    if (!flowOption)
        println("flowOption is null");
    if (!comment)
        println("comment is null");
    if (!user) {
        println("user is null");
        return;
    }
    println("begin");
    FLOW.interveneFlow(flowID, curNodeId, nextNodeIds, comment, user, doc);
    println("end");
}


/**
 * 流程回退操作
 * 使用示例：BackFlow(doc, "张三", "第三步", ["第二步"], "回退修改");
 * @param {Object} doc - 文档对象
 * @param {string} userName - 操作用户姓名
 * @param {string} curNodeLabel - 当前节点名称
 * @param {Array} nextNodeLabels - 回退到的节点名称数组
 * @param {string} comment - 回退原因
 */
function BackFlow(doc, userName, curNodeLabel, nextNodeLabels, comment) {
    var userId = getUserIdByUserName(userName);
    var user = getUserById(userId);         //当前用户对象为审批人
    var flowID = "";
    if (doc.getState() == null) {
        flowID = doc.getForm().getOnActionFlow();
    } else {
        flowID = doc.getState().getFlowid();
    }
    var curNodeId = GetCurrentNodeID(doc, curNodeLabel);
    var nextNodeIds = GetNextNodeIds(flowID, nextNodeLabels);
    var applicationId = getApplication();//获取applicationId
    var docProcess = getDocProcess(applicationId);
    var params = createParamsTable();
    params.setParameter("_flowid", flowID);
    var flowOption = "80";   //80提交 86干预、81回退
    if (!docProcess)
        println("docProcess is null");
    if (!doc) {
        println("doc is null");
        return;
    }
    if (!params)
        println("params is null");
    if (!curNodeId) {
        println("curNodeId is null");
        return;
    }
    if (!nextNodeIds) {
        println("nextNodeIds is null");
        return;
    }
    if (!flowOption)
        println("flowOption is null");
    if (!comment)
        println("comment is null");
    if (!user) {
        println("user is null");
        return;
    }
    FLOW.backFlow(flowID, curNodeId, nextNodeIds, comment, user, doc);
}

/**
 * 获取文档当前所在的流程节点
 * @param {Object} doc - 文档对象
 * @param {string} CurrentNodeName - 节点名称
 * @returns {Object} 节点对象,未找到返回undefined
 */
function GetCurrentNode(doc, CurrentNodeName) {
    var curDoc = doc;
    var state = curDoc.getState();
    //如果流程实例不为空
    if (state != null) {
        var noderts = state.getNoderts();
        //如果流程没有走完且在某个节点
        if (noderts != null && noderts.size() > 0) {
            //如果当前流程有多个节点取其中一个节点
            var nodert = noderts.iterator().next();
            debug("nodeId=" + nodert.getNodeid() + ", name=" + nodert.getName());
            if (isNotNull(CurrentNodeName) && CurrentNodeName.equals(nodert.getName()))
                return nodert
        }
    }
}

/**
 * 获取当前节点ID
 * @param {Object} doc - 文档对象
 * @param {string} CurrentNodeName - 节点名称
 * @returns {string} 节点ID
 */
function GetCurrentNodeID(doc, CurrentNodeName) {
    var node = GetCurrentNode(doc, CurrentNodeName);
    if (isNotNull(node))
        return node.getNodeid();
}

/**
 * 根据节点名称数组获取节点ID数组
 * @param {string} flowId - 流程ID
 * @param {Array} arrNodeName - 节点名称数组
 * @returns {Array} 节点ID数组
 */
function GetNextNodeIds(flowId, arrNodeName) {
    var nodes = FLOW.getAllNodesByFlowId(flowId);
    var result = [];
    var resultIndex = 0;
    for (var index = 0; index < arrNodeName.length; index++) {
        for (var nodeIndex = 0; nodeIndex < nodes.size(); nodeIndex++) {
            var noder = nodes.get(nodeIndex);
            var nodeName = arrNodeName[index];
            if (noder.getStatelabel().equals(nodeName)) {
                result[resultIndex] = noder.getId();
                resultIndex += 1;
            }
        }
    }
    return result;
}

/**
 * 打印流程所有节点名称及ID
 * @param {Object} doc - 文档对象
 * @returns {Object} 节点集合
 */
function PrintFlow(doc) {
    var state = doc.getState();
    var nodes = state.getNoderts();
    for (var it = nodes.iterator(); it.hasNext();) {
        var node = it.next();
        println(node.getName() + " : " + node.getId());
    }
    return nodes;
}

/**
 * 创建指定用户的用户列表（用于流程提交）
 * @param {string} userName - 用户姓名
 * @returns {ArrayList} 用户ID列表
 */
function SubmitTo(userName) {
    var userlist = createObject("java.util.ArrayList");
    if (isNotNull(userName)) {
        var userId = getUserIdByUserName(userName);
        debug("FlowUtils.SubmitTo userId=" + userId + ", userName=" + userName);
        if (isNotNull(userId))
            userlist.add(userId);
    }
    return userlist;
}

/**
 * 提交到文档指定字段登记的用户（单用户）
 * @param {Object} doc - 文档对象
 * @param {string} userField - 用户字段名
 * @returns {ArrayList} 用户列表
 */
function SubmitToUserField(doc, userField) {
    var value = doc.getItemValueAsString(userField);
    var userlist = createObject("java.util.ArrayList");
    if (value != "") {
        userlist.add(value);
    }
    return userlist;
}

/**
 * 根据功能代码和参数获取流程角色配置
 * @param {string} functionCode - 功能代码
 * @param {string} param1 - 参数1
 * @param {string} param2 - 参数2
 * @param {string} param3 - 参数3
 * @param {number} resultFieldIndex - 结果字段索引
 * @returns {string} 配置结果
 */
function FlowFunctionRole(functionCode, param1, param2, param3, resultFieldIndex) {
    var sql = "SELECT * FROM tlk_syfc_01_functionrole where ITEM_功能代码 = '" + functionCode + "' ";
    sql += " AND ITEM_参数1 = '" + param1 + "' ";
    if (isNotNull(param2))
        sql += " AND ITEM_参数2 = '" + param2 + "' ";
    if (isNotNull(param3))
        sql += " AND ITEM_参数3 = '" + param3 + "' ";
    println("sql=" + sql);
    var data = MagicFindBySql("magic", sql);
    if (data == null)
        return "";
    return data.get("ITEM_结果" + resultFieldIndex);
}

/**
 * 定时处理超时未提交的流程（定时任务）
 * 自动提交各种超时的流程到下一步骤
 * 执行频率：每小时执行一次
 */
function JobSubmitDelayDocs() {
    //同意采购申请单（超时10分钟自动提交）
    FindSubmitDoc("tlk_gy_cg_01_purchase_main", "智能助手", "同意采购", ["结束"], "", 10);
    //海外发货申请表（超时10分钟自动提交）
    FindSubmitDoc("tlk_ywreq_01_req_apply_main", "智能助手", "试算发货需求", ["数据校验"], "", 10);
    //海外发货申请表数据校验（超时10分钟自动提交）
    FindSubmitDoc("tlk_ywreq_01_req_apply_main", "智能助手", "数据校验", ["数据组复核"], "", 10);
    //采购订单结束提交到归档（延迟一个月后自动归档）
    FindSubmitDoc("tlk_gy_cg_01_order_main", "归档员", "结束", ["归档"], "", 43200);

}

/**
 * 高频率执行的流程处理任务
 * 处理采购流程中的准备采购和计算物料步骤
 */
function HighFrequency() {
    //准备采购
    var isEnd = CheckPDITaskIsEnd("JOB_OAFLOW_PURCHASE_DETAILS");
    if (isEnd) {
        debug("HighFrequency isEnd ( true == unlock ) : " + isEnd);
        FindSubmitOneDoc("tlk_gy_cg_01_purchase_main", "智能助手", "准备采购", ["计算物料"], "", 5);
    } else {
        debug("计算物料PDI任务已加锁。");
    }
    //计算物料
    //FindSubmitDoc("tlk_gy_cg_01_purchase_main", "智能助手", "计算物料", ["数据入库"], "", 3);
}

/**
 * 查找并提交超时的文档（批量处理）
 * @param {string} tableName - 表名
 * @param {string} userName - 用户名
 * @param {string} curNodeLabel - 当前节点名称
 * @param {Array} nextNodeLabels - 下一节点名称数组
 * @param {string} comment - 提交意见
 * @param {number} delayMinitus - 延迟分钟数
 */
function FindSubmitDoc(tableName, userName, curNodeLabel, nextNodeLabels, comment, delayMinitus) {
    //查询 tableName 中超时 delayMinitus 分钟 且当前节点为 curNodeLabel 当前审批人为 userName 的记录
    try {
        //提交到 nextNodeLabels 节点 审批意见为 comment
        var sql = "SELECT * FROM " + tableName + " WHERE STATELABEL LIKE '%" + curNodeLabel + "%' " +
            "AND AUDITORNAMES LIKE '%" + userName + "%' AND AUDITDATE < NOW() - INTERVAL " + delayMinitus + " MINUTE";
        var arr = MagicQueryBySql("magic", sql);
        if (arr != null && arr.size() > 0) {
            for (var j = 0; j < arr.size(); j++) {
                var vo = arr.get(j);
                var doc = findDocument(vo.get("ID"));
                if (isNotNull(doc))
                    SubmitFlow(doc, userName, curNodeLabel, nextNodeLabels, comment);
            }
        }
    } catch (e) {
        debug("FindSubmitDoc出错：" + e);
    }
}

/**
 * 查找并提交超时的文档（单个处理）
 * @param {string} tableName - 表名
 * @param {string} userName - 用户名
 * @param {string} curNodeLabel - 当前节点名称
 * @param {Array} nextNodeLabels - 下一节点名称数组
 * @param {string} comment - 提交意见
 * @param {number} delayMinitus - 延迟分钟数
 */
function FindSubmitOneDoc(tableName, userName, curNodeLabel, nextNodeLabels, comment, delayMinitus) {
    //查询 tableName 中超时 delayMinitus 分钟 且当前节点为 curNodeLabel 当前审批人为 userName 的记录
    //提交到 nextNodeLabels 节点 审批意见为 comment
    var sql = "SELECT * FROM " + tableName + " WHERE STATELABEL LIKE '%" + curNodeLabel + "%' " +
        "AND AUDITORNAMES LIKE '%" + userName + "%' AND AUDITDATE < NOW() - INTERVAL " + delayMinitus + " MINUTE " +
        "ORDER BY AUDITDATE ASC ";
    var data = MagicFindBySql("magic", sql);
    if (isNotNull(data)) {
        var doc = findDocument(data.get("ID"));
        SubmitFlow(doc, userName, curNodeLabel, nextNodeLabels, comment);
    } else {

    }
}

function Restart1(doc, firstNodeName) {
    var applicationid = SysApplicationID();
    var flowid = doc.getForm().getOnActionFlow();
    var user = doc.getAuthor();
    var billDefiDesignTimeService = Packages.cn.myapps.designtime.common.service.DesignTimeServiceManager.billDefiDesignTimeService();
    var flowVO = billDefiDesignTimeService.doView(applicationid,flowid);
    var flowRuntimeService = new Packages.cn.myapps.runtime.workflow.storage.runtime.ejb.FlowRuntimeServiceImpl(applicationid);
    var instance = flowRuntimeService.createTransientFlowStateRT(doc, flowid, user);
    doc.setState(instance);

    var startNode = FindNodeByName(doc, "开始");
    var startNodeId = startNode.getId();
    //发起人申请节点
    var firstNode = FindNodeByName(doc, firstNodeName);
    var firstNodeId = firstNode.getId();
    var params = getParamsTable();
    var submitTo = '[{"nodeid":"'+firstNodeId+'","userids":"[\''+user.getId()+'\']","isToPerson":"true"}]';
    params.setParameter("submitTo", submitTo);

    //启动流程
    flowRuntimeService.doApprove(instance, startNodeId, firstNodeId, "1", "", params, user, false, false);


}

function Restart(doc, firstNodeName){
    var docProcess = getDocProcess(SysApplicationID());
    var user = doc.getAuthor();
    var billParams = createParamsTable();
    var flowid = doc.getForm().getOnActionFlow();
    billParams.setParameter("_flowid", flowid);
    docProcess.doStartFlowOrUpdate(doc, billParams, user); // 启动流程
    debug("重启流程成功！");
}

function FindNodeByName(doc, nodeName) {
    flowId = doc.getForm().getOnActionFlow();
    var nodes = FLOW.getAllNodesByFlowId(flowId);
    for (var nodeIndex = 0; nodeIndex < nodes.size(); nodeIndex++) {
        var noder = nodes.get(nodeIndex);
        var name1 = noder.getStatelabel();
        var name2 =
            debug("noder.getStatelabel()=" + name1 + ", nodeName=" + nodeName);
        if (name1.equals(nodeName)) {
            debug("匹配");
            return noder;
        }
    }
    debug("没有匹配");
    return null;
}

function FindNodeByNames(doc, nodeNames) {
    var result = [];
    for (var nodeIndex = 0; nodeIndex < nodeNames.size(); nodeIndex++) {
        var nodeName = nodeNames[nodeIndex];
        var node = FindNodeByName(doc, nodeName);
        if (isNotNull(node))
            result[result.size()] = node;
    }
    return result;
}

function FindNodeIDsByNames(doc, nodeNames) {
    var result = [];
    var itemIndex = 0;
    for (var nodeIndex = 0; nodeIndex < nodeNames.length; nodeIndex++) {
        var nodeName = nodeNames[nodeIndex];
        var node = FindNodeByName(doc, nodeName);
        if (isNotNull(node)){
            result[itemIndex] = node.getId();
            itemIndex += 1;
        }
    }
    return result;
}

//提交
//SubmitFlow(doc, "罗杰城",  "第二步", ["第三步"],  "系统自动流转");
function SubmitByNodeName(doc, userName, curNodeLabel, nextNodeLabels, comment) {
    var userId = getUserIdByUserName(userName);
    var user = getUserById(userId);         //当前用户对象为审批人
    var flowID = doc.getForm().getOnActionFlow();
    var curNodeId = FindNodeByName(doc, curNodeLabel);
    if (isNotNull(curNodeId) == false) {
        debug(doc.getItemValueAsString("编号") + ":SubmitFlow找不到" + curNodeLabel + "节点");
        return;
    }
    var nextNodeIds = FindNodeIDsByNames(doc, nextNodeLabels);
    if (nextNodeIds.length < 1) {
        debug("SubmitFlow找不到" + nextNodeLabels + "节点");
        return;
    }
    var applicationId = SysApplicationID();//获取applicationId                            
    var docProcess = getDocProcess(applicationId);
    var params = createParamsTable();
    params.setParameter("_flowid", flowID);
    var flowOption = "80";   //80提交 86干预、81回退
    FLOW.submitFlow(flowID, curNodeId, nextNodeIds, comment, user, doc);
}
//根据doc获取下一节点名称
function getNextStatusLabelName(doc){
    var formId = doc.getFormid()
    var appId = getApplication()
    var docId = doc.getId()
    var url = "http://10.10.20.158:8080/obpm/api/runtime/"+appId+"/documents/"+docId+"/panels/submission";
    var jsonObject = new Packages.com.alibaba.fastjson.JSONObject();
    var document = new Packages.com.alibaba.fastjson.JSONObject();
    var items = new Packages.com.alibaba.fastjson.JSONObject();
    document.put("applicationId",appId)
    document.put("formId",formId)
    document.put("id",docId)
    document.put("items",items)
    jsonObject.put("document",document)
    var param= jsonObject.toString();
    var response = postUrlContext(url,param)
    var body = response.body()
    var jsonObj = JSON.parse(body);
    var nextNodes = jsonObj.data.nextNodes
    var result = []
    for(var i = 0; i<nextNodes.length; i++){
        var name = nextNodes[i].name
        result.push(name)
    }
    return result
}

//只适合单分支的节点 （用于代替自动节点）
function autoSubmitFlowByTable(table){
    var userName = "智能助手"
    var comment = "系统自动流转"
    var sql = "select * FROM "+ table +" where STATELABEL <> '结束' and AUDITORNAMES LIKE '%智能助手%' order by CREATED desc"
    var arr = MagicQueryBySql("magic", sql);
    if(arr.size() ===0){
        println(sql)
        return
    }
    for(var i =0; i< arr.size(); i++){
        var docId = arr.get(i).get("id")
        var doc = findDocument(docId)
        var nextNodeLabels= getNextStatusLabelName(doc)
        var state = doc.getState()
        if (state != null) {
            var noderts = state.getNoderts();
            //如果流程没有走完且在某个节点
            if (noderts != null && noderts.size() > 0) {
                //如果当前流程有多个节点取其中一个节点
                var nodert = noderts.iterator().next();
                var curNodeLabel = nodert.getName()
            }
            println(curNodeLabel)
            SubmitFlow(doc, userName, curNodeLabel, nextNodeLabels, comment)
        }
    }
}

//干预
//GoToAnyWhere(doc, "罗杰城",  "第二步", ["第三步"],  "系统自动流转");
function GoToAnyWhere(doc, userName, curNodeLabel, nextNodeLabels, comment) {
    debug("GoToAnyWhere test 1");
    var userId = getUserIdByUserName(userName);
    debug("GoToAnyWhere test 2");
    var user = getUserById(userId);         //当前用户对象为审批人
    debug("GoToAnyWhere test 3");
    var flowID = "";
    if (doc.getState() == null) {
        flowID = doc.getForm().getOnActionFlow();
    } else {
        flowID = doc.getState().getFlowid();
    }
    debug("GoToAnyWhere test 3.1");
    var curNodeId = FindNodeByName(doc, curNodeLabel);
    debug("GoToAnyWhere test 3.2");
    if (isNotNull(curNodeId) == false) {
        debug("GoToAnyWhere找不到curNodeLabel = " + curNodeLabel + "节点");
        return;
    }
    debug("GoToAnyWhere test 4");
    var nextNodeIds = GetNextNodeIds(flowID, nextNodeLabels);
    debug("GoToAnyWhere test 5");
    if (nextNodeIds.length < 1) {
        debug("GoToAnyWhere找不到nextNodeLabels = " + nextNodeLabels + "节点");
        return;
    }
    debug("GoToAnyWhere test 6");
    var applicationId = SysApplicationID();//获取applicationId                            
    debug("GoToAnyWhere test 7");
    var docProcess = getDocProcess(applicationId);
    debug("GoToAnyWhere test 8");
    var params = createParamsTable();
    params.setParameter("_flowid", flowID);
    var flowOption = "86";   //80提交 86干预、81回退
    if (!docProcess)
        println("docProcess is null");
    if (!doc) {
        println("doc is null");
        return;
    }
    if (!params)
        println("params is null");
    if (!curNodeId) {
        println("curNodeId is null");
        return;
    }
    if (!nextNodeIds) {
        println("nextNodeIds is null");
        return;
    }
    if (!flowOption)
        println("flowOption is null");
    if (!comment)
        println("comment is null");
    if (!user) {
        println("user is null");
        return;
    }
    println("begin");
    FLOW.interveneFlow(flowID, curNodeId, nextNodeIds, comment, user, doc);
    println("end");
}
```

---

## 5. SystemUtils

**模块说明：** 系统通用工具函数集合，提供部门管理、消息通知、文件操作、数据转换、日志记录等系统级基础功能。

**主要功能：**

- 用户部门信息查询和管理
- QQ消息和系统通知发送
- 文件读取和数据处理
- HTML和URL生成工具
- 数据类型转换和验证
- 系统日志记录和调试
- 文档克隆和数据备份
- PowerBI报表集成

**数据来源：**

- obpm5: 用户部门关系数据
- DATACENTER_BUSINESS: 消息发送数据
- magic: 系统日志数据
- 本地文件系统: SQL、HTML、CSV文件

**使用场景：**

- 用户权限和部门验证
- 系统消息和通知发送
- 数据导入导出处理
- 报表链接生成
- 系统运行日志记录
- 调试信息输出
- 文档数据备份

**注意事项：**

- 文件路径需要确保存在和可访问
- 消息发送需要配置正确的数据源
- 日志记录会占用存储空间，需定期清理
- 数据转换时注意类型安全
- PowerBI链接中的特殊字符需要编码
- 调试信息在生产环境应适当控制

**主要函数：**

```javascript
/**
 * 检查当前用户是否属于指定部门
 * @param {string} DepartmentNames - 部门名称，多个用逗号分隔
 * @returns {boolean} true表示用户属于其中任一部门
 */
function IsDepartment(DepartmentNames) {
  var userDepts = GetDepartmentsByCurrentUser();
  var opts = SplitString(DepartmentNames, ",");
  for (var i = 0; i < opts.length; i++) {
    for (var j = 0; j < userDepts.size(); j++) {
      if (opts.get(i).equals(userDepts.get(j))) {
        return true;
      }
    }
  }
  return false;
}

/**
 * 获取当前用户所属部门
 * @returns {Object} 部门选项列表
 */
function GetDepartmentsByCurrentUser() {
  var loginNo = getWebUser().getLoginno();
  return GetDepartmentsByUser(loginNo);
}

/**
 * 获取用户所属部门
 * @param {string} LoginNo - 用户登录账号
 * @returns {Object} 部门选项列表
 */
function GetDepartmentsByUser(LoginNo) {
  var opts = createOptions();
  opts.add("", "");
  if ("".equals(LoginNo))
    return opts;
  var sql = "select distinct t_department.NAME from t_user_department_role_set " +
    "left join t_user on t_user_department_role_set.USERID  = t_user.ID " +
    "left join t_department on t_user_department_role_set.DEPARTMENTID = t_department.ID " +
    "where t_user.LOGINNO = '" + LoginNo + "' and DOMAIN_ID = '" + getDomainid() + "'" +
    "order by t_department.LEVELS, t_department.SORTID";
  var datas = queryByDSName("obpm5", sql);
  if (datas != null) {
    for (var iterator = datas.iterator(); iterator.hasNext();) {
      var map1 = iterator.next();//取值
      var name = map1.get("NAME");
      var value = map1.get("NAME");
      opts.add(name, value);
    }
  }
  return opts;
}

//获取用户默认部门
function GetDetaulDepartmentByCurrentUser() {
  var loginNo = getWebUser().getLoginno();
  return GetDefaultDepartmentByUser(loginNo);
}

//获取当前用户默认部门
function GetDefaultDepartmentByUser(LoginNo) {
  if ("".equals(LoginNo))
    return "";
  var sql = "select t_department.NAME  from t_department right join t_user on t_department.ID = t_user.DEFAULTDEPARTMENT " +
    "where t_user.LOGINNO = '" + LoginNo + "'";
  var datas = queryByDSName("obpm5", sql);
  if (datas != null) {
    for (var iterator = datas.iterator(); iterator.hasNext();) {
      var map1 = iterator.next();//取值
      return map1.get("NAME");
    }
  }
  return "";
}


/**
 * 在系统新增选项卡通过记录打开
 * @param {Object} doc - 文档对象
 * @param {string} FieldName - 字段名称
 * @param {string} title - 标题
 * @returns {string} HTML链接字符串
 */
function OpenUITabByDoc(doc, FieldName, title) {
  var url = OpenDoc(doc);
  var label = doc.getItemValueAsString(FieldName);
  //var result = "<o-action action-type='jumpto' url='" + url + "' title='" + label +
  //  "' open-type='open-blank'>" + title + "</o-action>";
  var result = "<a style='color: blue;' href='" + url + "' target='_blank'>" + title + "</a>";

  return result;
}

//在浏览器新页面打开
function OpenTabByDoc(doc, FieldName) {
  var url = OpenDoc(doc);
  var label = doc.getItemValueAsString(FieldName);
  //return label;
  var result = "<o-action action-type='jumpto' url='" + url + "' title='" + label +
    "' open-type='open-blank'>" + label + "</o-action>";
  return result;
}

//通过编号打开记录
function OpenTabByDocNumber(TableName, FieldName, FieldValue) {
  var doc = LoadByIndexFieldValue(TableName, "ITEM_" + FieldName, FieldValue, true);
  println(doc)
  if (doc == null) {
    debug("找不到" + TableName + " : " + FieldValue);
    return FieldValue;
  } else
    return OpenTabByDoc(doc, FieldName);
}

//生成跳转地址脚本
function OpenDoc(doc) {
  var applicationId = getApplication();
  if (doc) {
    //打开页面
    var docId = doc.getId();
    var formId = doc.getFormid();
    var request = $WEB.getParamsTable().getHttpRequest();
    var urlt = "http://" + request.getServerName() + ":" + request.getServerPort() + "/static";
    var url = urlt + "/portal/vue/index.html#/open?appId=" + applicationId + "&linkType=00&actionContent=" + formId + "&docid=" + docId;
    return url;
  } else {
    return "";
  }
}

//克隆表单
function CloneDoc(sourceDoc, targetDoc) {
  var items = sourceDoc.getItems();
  for (var iter = items.iterator(); iter.hasNext();) {
    var item = iter.next();
    if (targetDoc.findItem(item.getName()) == null)
      debug("结构不一样目标对象缺少" + item.getName() + "字段");
    targetDoc.findItem(item.getName()).setValue(item.getValue()); //如果这行出错，说明表结构不一样目标对象缺少字段。
  }
}

function HtmlUrl(url, label, toolTip) {
  var html = "<table width='100%'>";
  html += "<a href='" + url + "' target='_blank' title='" + toolTip + "'>" + label + "</a>";
  return html;
}

/**
 * 发送QQ消息
 * @param {string} userName - 接收人用户名
 * @param {string} msgLevel - 消息级别
 * @param {string} content - 消息内容
 */
function SendQQMessage(userName, msgLevel, content) {
  var prefix = "SQM-";
  var uuid = countNext2(prefix, true, true, true, 4);
  var appId = "MAGIC_0001";
  var functionName = "";
  var tableName = "";
  var msgContent = content;
  var createTime = format(getToday(), "yyyy-MM-dd HH:mm:ss");
  var recipient = userName;
  var sql = "INSERT INTO MagicSendMessage.T_Message" +
    "(UUID, AppID, FunctionName, TableName, ObjectID, MsgKind, MsgContent, MsgLevel, IsSend, SendTime, CreateTime, Recipient)" +
    "VALUES('" + uuid + "', '" + appId + "', '" + functionName + "', '" + tableName + "', '', '', '" +
    msgContent + "', '" + msgLevel + "', 0, null, '" + createTime + "', '" + recipient + "')";
  insertByDSName("DATACENTER_BUSINESS", sql);
}

/**
 * 根据文档发送QQ消息
 * @param {Object} doc - 文档对象
 * @param {string} formName - 表单名称
 * @param {string} msgLevel - 消息级别
 * @param {string} content - 消息内容
 */
function SendQQMessageByDoc(doc, formName, msgLevel, content) {
  var prefix = "SQM-";
  var uuid = countNext2(prefix, true, true, true, 4);
  var appId = "MAGIC_0001";
  var functionName = doc.getFormname();
  var tableName = "";
  var objectId = doc.getId();
  var msgKind = doc.getStateLabel();
  var author = doc.getAuthor();
  if (author == null)
    return;
  var msgContent = "您在VOA系统中的【" + formName + "】审批工作，事项发起人是【" + author.getName() + "】，当前审批状态为【" + msgKind + "】," + content;
  var createTime = format(getToday(), "yyyy-MM-dd HH:mm:ss");
  var recipient = doc.getAuditorNames();
  var sql = "INSERT INTO MagicSendMessage.T_Message" +
    "(UUID, AppID, FunctionName, TableName, ObjectID, MsgKind, MsgContent, MsgLevel, IsSend, SendTime, CreateTime, Recipient)" +
    "VALUES('" + uuid + "', '" + appId + "', '" + functionName + "', '" + tableName + "', '" + objectId + "', '" + msgKind + "', '" +
    msgContent + "', '" + msgLevel + "', 0, null, '" + createTime + "', '" + recipient + "')";
  insertByDSName("DATACENTER_BUSINESS", sql);
}

function SendQQMessageByRole(roleName, doc, formName, msgLevel, content) {
  var roleId = getRoleIdByName(roleName);
  var users = getUsersByRoleId(roleId);
  var recipient = "";
  for (var iter = users.iterator(); users != null && iter.hasNext();) {
    var user = iter.next();
    if ("".equals(recipient) == false)
      recipient += ",";
    recipient += user.getName();
  }
  var prefix = "SQM-";
  var uuid = countNext2(prefix, true, true, true, 4);
  var appId = "MAGIC_0001";
  var functionName = doc.getFormname();
  var tableName = "";
  var objectId = doc.getId();
  var msgKind = doc.getStateLabel();
  var author = doc.getAuthor();
  var msgContent = "您在VOA系统中的【" + formName + "】审批工作，事项发起人是【" + author.getName() + "】，当前审批状态为【" + msgKind + "】," + content;
  var createTime = format(getToday(), "yyyy-MM-dd HH:mm:ss");

  var sql = "INSERT INTO MagicSendMessage.T_Message" +
    "(UUID, AppID, FunctionName, TableName, ObjectID, MsgKind, MsgContent, MsgLevel, IsSend, SendTime, CreateTime, Recipient)" +
    "VALUES('" + uuid + "', '" + appId + "', '" + functionName + "', '" + tableName + "', '" + objectId + "', '" + msgKind + "', '" +
    msgContent + "', '" + msgLevel + "', 0, null, '" + createTime + "', '" + recipient + "')";
  insertByDSName("DATACENTER_BUSINESS", sql);
}

//为一个options对象添加一行记录，添加前判断有没有重复
function AddOption(options, item) {
  if (!options)
    options = createOptions();
  if (!item)
    item = "";
  var result = createOptions();
  var isSame = false;
  for (var iter = options.getOptions().iterator(); iter.hasNext();) {
    var opt = iter.next();
    var option = opt.getOption();
    var value = opt.getValue();
    if (item.equals(value))
      isSame = true;
    result.add(option, value);
  }
  if (isSame == false)
    result.add(item, item);
  return result;
}

function AddOptionAndLabel(options, item, label) {
  if (!options)
    options = createOptions();
  if (!item)
    item = "";
  var result = createOptions();
  var isSame = false;
  for (var iter = options.getOptions().iterator(); iter.hasNext();) {
    var opt = iter.next();
    var option = opt.getOption();
    var value = opt.getValue();
    if (item.equals(value))
      isSame = true;
    result.add(option, value);
  }
  if (isSame == false)
    result.add(label, item);
  return result;
}

/**
 * 编码SKU中的特殊字符
 * @param {string} sku - 商品SKU
 * @returns {string} 编码后的SKU
 */
function EncodingSKU(sku) {
  if (isNotNull(sku) == false)
    return sku;
  var result = sku.replace("+", "%2B");
  result = result.replace("#", "%23");
  return result;
}

function OpenTabToPowerBI(sku, label) {
  sku = EncodingSKU(sku);
  var url = "http://10.10.21.36/reports/powerbi/%E5%95%86%E5%93%81%E5%9F%BA%E7%A1%80%E7%94%BB%E5%83%8F%E6%95%B0%E6%8D%AE-LJC-20240102?rs:embed=true&filter=SHOPELF_SKU%2FSKU%20eq%20%27" + sku + "%27'";
  var result = "<o-action action-type='jumpto' url='" + url + "' title='" + label + "' open-type='open-blank'>" + label + "</o-action>";
  return result;
}

function CreateImgToPowerBI(sku, label) {
  var imgUrl = "file:///H:/200-图片/210-产品图片/218单品识别图库/" + sku + ".jpg";
  var html = "<img src='" + imgUrl + "' width='100%' height='100%' />";
  //html = "<o-action action-type='image' url='" + url + "' title='" + label + "' open-type='open-tab'>" + label + "</o-action>";
  return html;
}

function CreateHtmlUrlToPowerBI(sku, label) {
  sku = EncodingSKU(sku);
  var html = "<table width='100%'>";
  html += "<tr height='38'><td style='padding: 0 2px;width: 100%;font-size: 16px;'><a href='http://10.10.21.36/reports/powerbi/%E5%95%86%E5%93%81%E5%9F%BA%E7%A1%80%E7%94%BB%E5%83%8F%E6%95%B0%E6%8D%AE-LJC-20240102?rs:embed=true&filter=SHOPELF_SKU%2FSKU%20eq%20%27" + sku + "%27' target='_blank'>" + label + "</a></td></tr>";
  html += "</table>";
  return html;
}
function CreateHtmlUrlToPowerBINEW(sku, label) {
  sku = EncodingSKU(sku);
  var html = "<table style='width: 100%; border-collapse: collapse;'><tr><td style='padding: 4px; text-align: center;'><a href='http://10.10.21.36/reports/powerbi/%E5%95%86%E5%93%81%E5%9F%BA%E7%A1%80%E7%94%BB%E5%83%8F%E6%95%B0%E6%8D%AE-LJC-20240102?rs:embed=true&filter=SHOPELF_SKU%2FSKU%20eq%20%27" + sku + "%27' target='_blank' style='display: inline-block; padding: 6px 10px; background: #2c7be5; color: white !important; text-decoration: none; border-radius: 3px; font-size: 12px; font-weight: 400; letter-spacing: 0.1px; transition: all 0.2s ease; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); width: auto; text-align: center;' onmouseover='this.style.transform=\"scale(1.01)\"; this.style.boxShadow=\"0 2px 3px rgba(0, 0, 0, 0.06)\";' onmouseout='this.style.transform=\"scale(1)\"; this.style.boxShadow=\"0 1px 2px rgba(0, 0, 0, 0.05)\";'>"
  html += label
  html += "</a></td></tr></table>";
  return html
}

function ShowFullField(doc, labelField, arr) {
  var itemMap = doc.getItemMap().toString();
  var items = splitString(itemMap, ",");
  var result = "";
  if (arr != null && arr.size() > 0) {
    for (var i = 0; i < arr.size(); i++) {
      var line = arr.get(i);
      var subStrs = splitString(line, ":");
      var value = GetDocValue(items, subStrs[0], subStrs[1]);
      if (isNotNull(value)) {
        isMatch = true;
        if ("".equals(result) == false)
          result += "\n";
        result += value;
      }
    }
  } else {
    result = itemMap.replaceAll(",", "\n");
  }
  //result += NotMatchField(items, arr);
  return "<p title='" + result + "'>" + doc.getItemValueAsString(labelField) + "</p>";
}

function GetDocValue(arr, sourceLabel, targetLabel) {
  for (var i = 0; i < arr.length; i++) {
    var line = arr[i];
    if (line.indexOf(sourceLabel) == 1) {
      //匹配，替换
      var result = line.replaceAll(sourceLabel, targetLabel);
      result = result.replaceAll("null", "");
      return result;
    }
  }
  return targetLabel + "=_";
}

function NotMatchField(dataItems, fieldItems) {
  var result = "";
  for (var i = 0; i < dataItems.length; i++) {
    var dataLine = dataItems[i];
    var dataArr = splitString(dataLine, "=");
    var isMatch = false;
    if (fieldItems != null) {
      for (var j = 0; j < fieldItems.size(); j++) {
        var fieldLine = fieldItems.get(j);
        var fieldArr = splitString(fieldLine, ":");
        if (dataArr[0] == fieldArr[0]) {
          isMatch = true;
          break;
        }
        if (isMatch == false) {
          if ("".equals(result))
            result += "\n";
          result += dataLine;
        }
      }
    } else {
      if ("".equals(result))
        result += "\n";
      result += dataLine;
    }
  }
  return result;
}

function PrintObj(obj) {
  for (var key in obj) {
    var value = obj[key];
    // 对每个属性进行操作，比如打印出来
    println("Start========" + key + "========Start");
    println(value);
    println("End========" + key + "========End");
  }
}

/**
 * 读取文本文件内容
 * @param {string} fileName - 文件路径
 * @returns {string} 文件内容
 */
function ReadTxt(fileName) {
  var CHARSET_NAME = "GBK"; //UTF-8
  try {
    var br = new Packages.java.io.BufferedReader(
      new Packages.java.io.InputStreamReader(
        new Packages.java.io.FileInputStream(fileName), CHARSET_NAME));
    var result = "";
    var line;
    while ((line = br.readLine()) != null) {
      result += line + "\n";
    }
    return result;
  } catch (e) {
    println(fileName + " 文件不存在！");
    //println(e);
    return "";
  }
}

/**
 * 读取SQL文件内容
 * @param {string} fileName - SQL文件名（不含扩展名）
 * @returns {string} SQL语句内容
 */
function ReadSql(fileName) {
  var path = "C:\\MyApps\\5_1\\myapps\\storage\\workspace\\sql\\";
  return ReadTxt(path + fileName + ".sql");

}
function ReadCsv(fileName, separator) {
  var config = new Packages.cn.hutool.core.text.csv.CsvReadConfig();
  config.setHeaderLineNo(0);
  var reader = Packages.cn.hutool.core.text.csv.CsvUtil.getReader(config);
  reader.setFieldSeparator(separator);
  var data = reader.read(Packages.cn.hutool.core.io.FileUtil.file(fileName));
  return data.getRows()
  /**
  下面是例子
  for (var i = 0; i < rows.size(); i++) {
  var row = rows.get(i)
  var line_Number = row.getOriginalLineNumber();
  var Merchant_SKU =row.getByName("Merchant SKU");
  }
*/
}
function ReadMagicTsv(fileName, param) {
  var config = new Packages.cn.hutool.core.text.csv.CsvReadConfig();
  config.setHeaderLineNo(7);
  var reader = Packages.cn.hutool.core.text.csv.CsvUtil.getReader(config);
  reader.setFieldSeparator('\t');
  var data = reader.read(Packages.cn.hutool.core.io.FileUtil.file(fileName));
  var jsonObject = new Packages.com.alibaba.fastjson.JSONObject();
  var headRows = data.getRows().subList(0, 6);
  for (var i = 0; i < headRows.size(); i++) {
    var row = headRows.get(i);
    var item = row.get(0);
    var detail = row.get(1);
    jsonObject.put(item, detail);
  }
  if ("row".equals(param)) {
    return data.getRows();
  } else if ("head".equals(param)) {
    return jsonObject
  } else {
    return "你输入param的不是row,也不是head"
  }
  /**
   for (var i = 0; i < rows.size(); i++) {
   var row = rows.get(i);
   if (row.getOriginalLineNumber() <= 6) continue;
   var line_Number = row.getOriginalLineNumber();
   var Merchant_SKU =row.getByName("Merchant SKU");
   break;
   }
   */

}


function ReplaceAll(str, source, target) {
  if (isNotNull(source) == false)
    return str;
  var maxAmount = 0;
  var sourceStr = "{" + source + "}";
  while (str.indexOf(sourceStr) > 0 && maxAmount < 100) {
    str = str.replace(sourceStr, target);
    maxAmount += 1;
  }
  return str;
}

//为文本框添加日志
//logField为日志记录字段
//targetField为监控修改情况的字段
function TextInputAddLog(user, doc, logField, targetField) {
  var log = doc.getItemValueAsString(logField);
  var msg = doc.getItemValueAsString(targetField);
  if (isNotNull(log) == false && isNotNull(msg) == false)
    return "";
  if (TextInputIsChange(log, msg) == false) {
    return log;
  }
  var dateStr = format(getToday(), "yyyy-MM-dd HH:mm:ss") + "  ";
  var userName = user.getName() + " : ";
  if (isNotNull(log))
    log = ";" + log;
  log = userName + msg + log;
  return log;
}

function TextInputIsChange(log, value) {
  //从已记录的日志判断被监控的输入内容是否有变化
  if (isNotNull(log) == false && isNotNull(value))
    return true;
  var lines = splitString(log, ";");
  var lastLine = lines[0];
  if (isNotNull(lastLine) == false && isNotNull(value))
    return true;
  var parts = splitString(lastLine, " ");
  if (parts.length >= 2) {
    return parts[2].equals(value) == false;
  } else {
    return true;
  }
}

//调试打印内容
function debug(msg) {
  var dateStr = format(getToday(), 'yyyy-MM-dd HH:mm:ss');
  var prefix = dateStr + " *************** : ";
  Packages.java.lang.System.out.println(prefix + msg);
  println(prefix + msg);
}

//转换成数值
function ToDouble(value) {
  if (isNotNull(value) == false)
    return 0;
  if (isNumberText(value))
    return parseDouble(value);
  else
    return 0;
}

//转换成整数
function ToInt(value) {
  if (isNotNull(value) == false)
    return 0;
  if (isNumberText(value))
    return parseInt(value);
  else
    return 0;
}

//检查状态和字段校验
function checkteStatusAndField(status, field) {
  var statusLable = getStateLabel()
  var condition = getItemValueAsString(field)
  if (statusLable.equals(status) && !isNotNull(condition)) {
    return "请输入" + field
  }
  return
}

function ReadHtml(fileName) {
  var path = "C:\\MyApps\\5_1\\myapps\\storage\\workspace\\html\\";
  return ReadTxt(path + fileName + ".html");

}

//添加日志
function AddLog(logLevel, doc, user, msg) {
  if (doc == null || user == null || isNotNull(msg) == false)
    return;
  try {

    var applicationId = SysApplicationID();
    var docProcess = getDocProcess(applicationId);
    //创建                          
    var cgdPrefix = "LOG-";
    var cgBillNo = countNext2(cgdPrefix, true, true, true, 4);
    var billFormName = "SYLOG_01_OALog";
    var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
    var Form = formProcess.doViewByFormName(billFormName, applicationId);
    var params = createParamsTable();
    var newDoc = docProcess.doNew(Form, user, params);
    newDoc.addStringItem("编号", cgBillNo);
    newDoc.addStringItem("表单名称", doc.getFormname());
    newDoc.addStringItem("DOCID", doc.getId());
    newDoc.addStringItem("操作人", user.getName());
    newDoc.addDateItem("操作时间", getToday());
    newDoc.addStringItem("日志级别", logLevel);
    newDoc.addStringItem("日志内容", msg);
    docProcess.doCreate(newDoc);//创建   
  } catch (e) {

  }
}

function info(doc, user, msg) {
  AddLog("info", doc, user, msg);
}

function debugd(doc, user, msg) {
  AddLog("debug", doc, user, msg);
}

function warning(doc, user, msg) {
  AddLog("warning", doc, user, msg);
}

function error(doc, user, msg) {
  AddLog("error", doc, user, msg);
}

//添加日志
function AddLogMsg(logLevel, msg) {
  if (isNotNull(msg) == false)
    return;
  try {
    var user = GetAssistant();
    var applicationId = SysApplicationID();
    var docProcess = getDocProcess(applicationId);
    //创建                          
    var cgdPrefix = "LOG-";
    var cgBillNo = countNext2(cgdPrefix, true, true, true, 4);
    var billFormName = "SYLOG_01_OALog";
    var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
    var Form = formProcess.doViewByFormName(billFormName, applicationId);
    var params = createParamsTable();
    var doc = docProcess.doNew(Form, user, params);
    doc.addStringItem("编号", cgBillNo);
    doc.addStringItem("表单名称", "");
    doc.addStringItem("DOCID", "");
    doc.addStringItem("操作人", user.getName());
    doc.addDateItem("操作时间", getToday());
    doc.addStringItem("日志级别", logLevel);
    doc.addStringItem("日志内容", msg);
    docProcess.doCreate(doc);//创建   
  } catch (e) {

  }
}

function infom(msg) {
  AddLogMsg("info", msg);
}

function debugm(msg) {
  AddLogMsg("debug", msg);
}

function warningm(msg) {
  AddLogMsg("warning", msg);
}

function errorm(msg) {
  AddLogMsg("error", msg);
}

function AddDocLog(doc, user, IndexFieldName, pd, ParentIndexFieldName) {
  if (doc == null || user == null)
    return;
  var applicationId = getApplication();//获取applicationId                            
  var docProcess = getDocProcess(applicationId);
  //创建表单                                                       
  var billFormName = "SYLOG_02_DocLog";
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
  var Form = formProcess.doViewByFormName(billFormName, applicationId);
  var params = createParamsTable();
  var ld = docProcess.doNew(Form, user, params);
  if (isNotNull(pd)){
    ld.addStringItem("ParentID", pd.getId());
    if (isNotNull(ParentIndexFieldName)){
      debug(pd);
      debug("ParentIndexFieldName=" + ParentIndexFieldName + ", value = " + pd.getItemValueAsString(ParentIndexFieldName));
      ld.addStringItem("上级编号", pd.getItemValueAsString(ParentIndexFieldName));
    }
    ld.addStringItem("上级表名", pd.getFormname());
  }
  ld.addStringItem("ID", doc.getId());
  if (isNotNull(IndexFieldName))
    ld.addStringItem("编号", doc.getItemValueAsString(IndexFieldName));
  ld.addStringItem("表名", doc.getFormname());
  //var jo = Packages.net.sf.json.JSONObject.fromObject(doc);
  ld.addStringItem("内容", doc);
  ld.addStringItem("编辑人", user.getName());
  ld.addDateItem("编辑时间", getToday());
  docProcess.doCreate(ld);//创建  
}

function AddChildDocsLog(pd, parentIndexFieldName, user, ChildTableName, IndexFieldName) {
  if (pd == null || user == null || isNotNull(ChildTableName) == false)
    return;
  var sql = "SELECT * FROM " + ChildTableName + " WHERE PARENT = '" + pd.getId() + "'";
  var query = queryBySQL(sql);
  if (query != null) {
    for (var iter = query.iterator(); iter.hasNext();) {
      var doc = iter.next();
      AddDocLog(doc, user, IndexFieldName, pd, parentIndexFieldName);
    }
  }
}

//100个以下的按5向上取整
//100~1000的按10向上取整
//1000~以上的按100向上取整
function CeilAmount(value){
    if (isNotNull(value) == false)
        return 0;
    if (value < 100)
        return ceilAmountByStep(value, 5);
    else if (value >= 100 && value < 1000)
        return ceilAmountByStep(value, 10);
    else
        return ceilAmountByStep(value, 100);
}

function ceilAmountByStep(value, step){
    if (step == 0)
        return value;
    var amount = round((value / step), 0);
    var point = value % step;
    if (point > 0)
        return (amount + 1) * step;
    else
        return amount * step;
}

function selectDocs(selected){
  var arrList = new Packages.java.util.ArrayList();
  var ids = splitString(selected, ";");
  for(var i = 0;i<ids.length;i++){
    var doc = findDocument(ids[i])
    if(isNotNull(doc)){
    arrList.add(doc)

    }
  }
  return arrList
}
```

---

## 6. DBUtils

**模块说明：** 数据库操作相关的函数集合，提供数据查询、加载、更新、统计等数据库操作的封装和优化。

**主要功能：**

- 数据查询和加载（单条、多条、子文档）
- 用户权限相关的SQL构建
- 数据统计和同步监控
- 工作流状态查询
- 字段值更新和文档操作
- 数据库连接管理
- 批量数据处理

**数据来源：**

- magic: 系统主数据库
- obpm5: 用户和流程数据
- DATACENTER_BUSINESS: 业务数据中心
- DATACENTER_SHOPELF: 商品数据中心
- 各种外部数据源

**使用场景：**

- 业务数据查询和统计
- 用户权限过滤
- 数据同步状态监控
- 工作流历史查询
- 批量数据操作
- 数据一致性检查

**注意事项：**

- SQL注入防护，使用参数化查询
- 大数据量查询需要分页处理
- 数据库连接需要及时释放
- 跨数据库查询注意性能影响
- 用户权限过滤确保数据安全
- 统计任务避免在高峰期执行

**主要函数：**

```javascript
/**
 * 根据索引字段值加载文档
 * @param {string} TableName - 表名
 * @param {string} IndexField - 索引字段名
 * @param {string} FieldValue - 字段值
 * @param {boolean} IsQuater - 是否加引号
 * @returns {Object} 文档对象，未找到返回undefined
 */
function LoadByIndexFieldValue(TableName, IndexField, FieldValue, IsQuater){
  if ("".equals(TableName)){
    debug("DBUtils.LoadByIndexFieldValue:TableName 不能为空");
    return;
  }
  if ("".equals(IndexField)){
    debug("DBUtils.LoadByIndexFieldValue:IndexField不能为空");
    return;
  }
  if ("".equals(FieldValue)){
    debug("DBUtils.LoadByIndexFieldValue:FieldValue不能为空");
    return;
 }
  var valueStr = FieldValue;
  if (IsQuater == true)
    valueStr = "'" + valueStr + "'";
  var sql = "SELECT * FROM " + TableName + " WHERE " + IndexField + " = " + valueStr;
var datas = queryBySQL(sql);
  if (datas.size() > 0) {
    for (var iter = datas.iterator(); iter.hasNext();) {
      var data = iter.next();
      return data;
    }
  }
}

/**
 * 构建用户权限过滤的SQL查询（备份版本）
 * @param {string} sql - 原始SQL查询
 * @param {string} orderPart - 排序部分
 * @returns {string} 带用户权限过滤的SQL
 */
function UserSql_backup(sql, orderPart){
      var userId = getWebUser().getId();
  var sql = "select distinct tab_main.* from (" + 
            sql + ") as tab_main left join " + 
                                "(select DOC_ID, ACTORID from t_actorrt union  all " +
            "select DOC_ID, ACTORID from t_actorhis union  all " +
            "select DOC_ID, ACTORID from t_flowhistory) as tab_flow " +
            "on tab_main.id = tab_flow.DOC_ID WHERE 1 = 1 ";
  sql += " AND (tab_flow.ACTORID = '" + userId + "' or tab_main.AUTHOR = '" + userId + "') ";
  sql += "ORDER BY " + orderPart;
  println(sql);
  return sql;
}

/**
 * 构建用户权限过滤的SQL查询
 * @param {string} sql - 原始SQL查询
 * @param {string} orderPart - 排序部分
 * @returns {string} 带用户权限过滤的SQL
 */
function UserSql(sql, orderPart) {
    var userId = getWebUser().getId();
    var sql = "select distinct tab_main.* from (" +
        sql + ") as tab_main left join " +
        "(select DOC_ID, ACTORID from t_actorrt where ACTORID='"+userId+"'" +" union all "+
        "select DOC_ID, ACTORID from t_actorhis  where ACTORID='"+userId+"'" +" union all "+
        "select DOC_ID, ACTORID from t_flowhistory where ACTORID='"+userId+"') as tab_flow " +
        "on tab_main.id = tab_flow.DOC_ID WHERE 1 = 1 ";
    sql += " AND (tab_flow.ACTORID = '" + userId + "' or tab_main.AUTHOR = '" + userId + "') ";
    sql += "ORDER BY " + orderPart;
    println(sql);
    return sql;
}

/**
 * 构建带用户字段的权限过滤SQL查询
 * @param {string} sql - 原始SQL查询
 * @param {string} orderPart - 排序部分
 * @param {string} userFieldName - 用户字段名
 * @returns {string} 带用户权限过滤的SQL
 */
function UserSqlWithField(sql, orderPart, userFieldName){
      var userId = getWebUser().getId();
  var sql = "select distinct tab_main.* from (" + 
            sql + ") as tab_main left join " + 
                                "(select DOC_ID, ACTORID from t_actorrt union  all " +
            "select DOC_ID, ACTORID from t_actorhis union  all " +
            "select DOC_ID, ACTORID from t_flowhistory) as tab_flow " +
            "on tab_main.id = tab_flow.DOC_ID WHERE 1 = 1 ";
  sql += " AND (tab_flow.ACTORID = '" + userId + "' or tab_main.AUTHOR = '" + userId + "') ";
  if (userFieldName)
    sql += " OR " + userFieldName + " = '" + getWebUser().getName() + "' ";
  sql += "ORDER BY " + orderPart;
  //debug(sql);
  return sql;
}


/**
 * 加载子文档列表
 * @param {string} TableName - 子表名
 * @param {string} ParentID - 父文档ID
 * @returns {ArrayList} 子文档列表
 */
function LoadChildDocs(TableName, ParentID){
  var sql = "SELECT * FROM " + TableName + " WHERE PARENT = '" + ParentID + "'";
  //debug(sql);
  var array = createObject("java.util.ArrayList");
  var datas = queryBySQL(sql);
  if (datas.size() > 0) {
    for (var iter = datas.iterator(); iter.hasNext();) {
      var data = iter.next();
      array.add(data);
    }
  }
  return array;
}

/**
 * 通过SQL查询数据并返回列表
 * @param {string} DsName - 数据源名称
 * @param {string} Sql - SQL查询语句
 * @returns {ArrayList} 查询结果列表
 */
function MagicQueryBySql(DsName, Sql){
  var query = queryByDSName(DsName,Sql);
  var array = createObject("java.util.ArrayList");
  if (query != null){
    for (var iterator = query.iterator(); iterator.hasNext();) {
      var datas = iterator.next();
      array.add(datas);
    }
  }
  return array; 
}

/**
 * 通过SQL查询单条数据
 * @param {string} DsName - 数据源名称
 * @param {string} Sql - SQL查询语句
 * @returns {Object} 查询结果对象，未找到返回null
 */
function MagicFindBySql(DsName, Sql){
  var query = queryByDSName(DsName,Sql);
  var array = createObject("java.util.ArrayList");
  if (query != null){
    for (var iterator = query.iterator(); iterator.hasNext();) {
      var datas = iterator.next();
      return datas;
    }
  }
  return null; 
}

/**
 * 通过SQL查询数据并返回Map集合
 * @param {string} DsName - 数据源名称
 * @param {string} Sql - SQL查询语句
 * @param {string} keyFieldName - 作为Map键的字段名
 * @returns {HashMap} 查询结果Map集合
 */
function MagicMapBySql(DsName, Sql, keyFieldName){
  var query = queryByDSName(DsName,Sql);
  var hashMap = new Packages.java.util.HashMap();  
  if (query != null){
    for (var iterator = query.iterator(); iterator.hasNext();) {
      var data = iterator.next();
      hashMap.put(data.get(keyFieldName), data);
    }
  }
  return hashMap; 
}

/**
 * 构建工作流状态查询SQL
 * @param {string} tableName - 表名
 * @param {string} IndexFieldName - 索引字段名
 * @param {string} StateLabel - 状态标签
 * @param {string} Filter - 过滤条件
 * @returns {string} 工作流状态查询SQL
 */
function WorkFlowStateSql(tableName, IndexFieldName, StateLabel, Filter){
  var domainid = getDomainid();
  var sql = "SELECT '" + domainid + "' as domainId, " + 
      "CONCAT(tabA." + IndexFieldName + ",'-',tabFlow.STARTNODENAME, '-', " +
          "tabFlow.STARTNODENAME, '-', tabFlow.NAME, '-', tabFlow.ACTIONTIME) AS id, " +
      "tabA." + IndexFieldName + " AS ITEM_编号, tabFlow.NAME AS ITEM_审批人, tabFlow.STARTNODENAME AS ITEM_审批环节, " +
      "tabFlow.ENDNODENAME AS ITEM_下个审批环节, tabFlow.ATTITUDE AS ITEM_签核意见, " +
      "tabFlow.ACTIONTIME AS ITEM_办理时间, " +
      "tabFlow.TIME_DIFFERENCE as ITEM_耗时 " +
      "FROM " +
      "(" +
          "SELECT *," +
          " (CASE WHEN @current_doc_id = DOC_ID THEN @row_number := @row_number + 1 ELSE @row_number := 1 END) AS SEQUENCE_NUMBER," +
          " @current_doc_id := DOC_ID AS dummy," +
          " (CASE " +
          " WHEN @row_number = 1 THEN 0 " +
          " WHEN @row_number > 1 AND STARTNODENAME = '' then (TIMESTAMPDIFF(hour,@prev_actiontime,NOW())) " +
                  " ELSE TIMESTAMPDIFF(hour,@prev_actiontime,ACTIONTIME) " +
                  " END) AS TIME_DIFFERENCE," +
          " IF(@row_number = 1, 0, TIMESTAMPDIFF(HOUR, ACTIONTIME, NOW())) AS LAST_TIME_DIFFERENCE," +
          " @prev_actiontime := ACTIONTIME " +
          "FROM (" +
          "SELECT DOC_ID, ACTORID, NODERT_ID, t_actorrt.NAME, '' as STARTNODENAME, t_nodert.NAME as ENDNODENAME, '' as ATTITUDE, t_nodert.ACTIONTIME, 't_actorrt' as tabName " +
            "FROM t_actorrt LEFT JOIN t_nodert ON t_actorrt.NODERT_ID = t_nodert.ID " +
            "UNION " +
            "SELECT ta.DOC_ID, ta.ACTORID, ta.NODEHIS_ID, ta.NAME, tr.STARTNODENAME, tr.ENDNODENAME, tr.ATTITUDE, tr.PROCESSTIME, 't_actorhis' as tabName  " +
            "FROM t_actorhis ta LEFT JOIN t_relationhis tr ON ta.NODEHIS_ID = tr.ID " +
            "UNION " +
            "SELECT DOC_ID, ACTORID, NODEHIS_ID, t_flowhistory.NAME, t_flowhistory.STARTNODENAME, t_flowhistory.ENDNODENAME, t_flowhistory.ATTITUDE, t_flowhistory.ACTIONTIME, 't_flowhistory' as tabName " +
            "FROM t_flowhistory " +
            ") AS subquery " +
            "CROSS JOIN (SELECT @row_number := 0, @current_doc_id := '', @prev_actiontime := NULL) AS vars " +
            "ORDER BY DOC_ID, ACTIONTIME, STARTNODENAME DESC " +
          ") tabFlow LEFT JOIN " +
      "((SELECT ID, " + IndexFieldName + " FROM " + tableName + " tgcpm WHERE STATELABEL IN(" + StateLabel + "))) AS tabA " +
      "ON tabFlow.DOC_ID = tabA.ID " +
      "WHERE tabA.ID is not null AND " + Filter + " " +
      "ORDER BY " + IndexFieldName + ", ACTIONTIME, STARTNODENAME DESC ";
   return sql;
}

/**
 * 清除所有子文档
 * @param {string} tableName - 子表名
 * @param {string} ParentID - 父文档ID
 */
function ClearAllChildDocs(tableName, ParentID){
  if (isNotNull(ParentID) == false)
    return;
  var arr = LoadChildDocs(tableName, ParentID);
  if (arr != null && arr.size() > 0){
    //实例化文档对象
    var applicationId = getApplication();//获取applicationId                            
    var docProcess = getDocProcess(applicationId); 
    for(var j =0; j < arr.size(); j++){
      var vo = arr.get(j);
      docProcess.doRemove(vo.getId()); 
    }
  }
}

/**
 * 更新表单字段值
 * @param {Object} doc - 文档对象
 * @param {string} fieldName - 字段名
 * @param {string} dataKind - 数据类型（String、Double等）
 * @param {*} value - 字段值
 * @returns {boolean} 更新是否成功
 */
function UpdateFieldValue(doc, fieldName, dataKind, value){
  if (doc == null)
    return false;
  doc.removeItem(fieldName);
  if ("String".equals(dataKind)){
   doc.addStringItem(fieldName, value) ;
    return true
  }
  else if ("Double".equals(dataKind))
    doc.addDoubleItem(fieldName, value);
  else if ("Date".equals(dataKind))
    doc.addDateItem(fieldName, value);
  else if ("Text".equals(dataKind))
    doc.addTextItem(fieldName, value);
  else if ("Long".equals(dataKind))
    doc.addLongItem(fieldName, value);
  else if ("Float".equals(dataKind))
    doc.addFloatItem(fieldName, value);
  else if ("Int".equals(dataKind))
    doc.addIntItem(fieldName, value);

}

//获取SQL中某列的所有值
//DSName 数据库名称
//Sql
//FieldName 字段名
//Quater true时为值内容增加单引号
//ReturnString true时返回String false时返回arr
function MagicGetItemValue(DSName, sql, FieldName, Quater, ReturnString){
    var arr = MagicQueryBySql(DSName, sql);
    if (isNotNull(arr) == false)
        return null;
    var str = "";
    var vos = createObject("java.util.ArrayList");
    if (arr != null && arr.size() > 0){
    for(var j =0; j < arr.size(); j++){
      var vo = arr.get(j);
      var value = vo.get(FieldName);
      if (Quater)
        value = "'" + value + "'";
      if (ReturnString){
        if (isNotNull(str))
            str += ",";
        str += value;
      } else {
        vos.add(vo);
      }
    }
    if (ReturnString)
        return str;
    else
        return vos;
  }
}

//获取用户指定流程代理的所有用户
function GetAgents(user, flowId) {
    if (isNotNull(user) == false || isNotNull(flowId) == false)
        return null;
    var sql = "select distinct tf.OWNER, tu.NAME from magic5.t_flow_proxy tf " +
        "left join obpm5.t_user tu on tf.OWNER = tu.ID " +
        "where tf.ENDPROXYTIME >= Now() AND locate('" + user.getId() + "', tf.AGENTS) and (tf.FLOWID = '' or tf.FLOWID = '" + flowId + "')";
    debug("DBUtils.GetAgents.sql = " + sql);
    var arr = MagicQueryBySql("magic", sql);
    var result = new Packages.java.util.HashMap();
    result.put(user.getId(), user);
    if (arr != null && arr.size() > 0) {
        for (var j = 0; j < arr.size(); j++) {
            var vo = arr.get(j);
            var proxer = getUserById(vo.get("OWNER"));
            if (isNotNull(proxer))
                result.put(vo.get("OWNER"), proxer);
        }
    }
    return result;
}

//以用户字段过滤用户（包含代理人）的数据
function UserSqlByUserField(sql, userField, users) {
    sql = "SELECT * FROM (" + sql + ") TabMain ";
    var wherePart = "";
    var userIds = users.keySet();
    for (var iter = userIds.iterator(); iter.hasNext();) {
        var userId = iter.next();
        var user = users.get(userId);
        if (isNotNull(wherePart))
          wherePart += " OR ";
        wherePart += " LOCATE('" + userId + "', concat(AUDITORLIST, AUTHOR)) > 0";
        wherePart += " OR LOCATE('" + user.getName() + "', ITEM_" + userField + ") > 0 ";
    }
    return sql += " WHERE 1 = 1 AND (" + wherePart + ") ";
}

//统计普源与数据中心数据差异
function ExecStatisticsForPY(){
    const ShopElfName = "普源同步数据中心";
    const lines = ['B_Goods-B_Goods-SHOPELF.B_Goods', 
                'B_Supplier-B_Supplier-SHOPELF.B_Supplier',
                'S_SupplierPurview-S_SupplierPurview-SHOPELF.S_SupplierPurview',
                'KC_CurrentStock-KC_CurrentStock-SHOPELF.KC_CURRENTSTOCK',
                'B_StockIn_AbnormalM-B_StockIn_AbnormalM-SHOPELF.B_StockIn_AbnormalM',
                'KC_StockCheckD-KC_StockCheckD-SHOPELF.KC_StockCheckD',
                'KC_StockCheckM-KC_StockCheckM-SHOPELF.KC_StockCheckM',
                'B_GoodsSKULocationLog-B_GoodsSKULocationLog-SHOPELF.B_GoodsSKULocationLog',
                'B_GoodsCats-B_GoodsCats-SHOPELF.B_GoodsCats',
                'CK_StockOutD-CK_StockOutD-SHOPELF.CK_STOCKOUTD',
                'CK_StockOutM-CK_StockOutM-SHOPELF.CK_STOCKOUTM',
                'XS_SaleAfterD-XS_SaleAfterD-SHOPELF.XS_SALEAFTERD',
                'XS_SaleAfterM-XS_SaleAfterM-SHOPELF.XS_SALEAFTERM',
                'P_TradeUn_His-P_TradeUn_His-SHOPELF.P_TRADEUN_HIS',
                'P_TradeDtUn-P_TradeDtUn-SHOPELF.P_TRADEDTUN',
                'P_TradeUn-P_TradeUn-SHOPELF.P_TRADEUN',
                'P_TradeSend-P_TradeSend-SHOPELF.P_TradeSend',
                'P_TradeDt_His-P_TradeDt_His-SHOPELF.P_TRADEDT_HIS',
                'P_Trade_His-P_Trade_His-SHOPELF.P_TRADE_HIS',
                'P_Trade_bdt-P_Trade_bdt-SHOPELF.P_TRADE_BDT',
                'P_Trade_b-P_Trade_b-SHOPELF.P_TRADE_B',
                'M_eBayCase-M_eBayCase-SHOPELF.M_EBAYCASE',
                'M_eBayCaseD-M_eBayCaseD-SHOPELF.M_EBAYCASED',
                'KC_wanytInventory-KC_wanytInventory-SHOPELF.KC_WANYTINVENTORY',
                'KC_StockSplitM-KC_StockSplitM-SHOPELF.KC_STOCKSPLITM',
                'KC_StockChangeD-KC_StockChangeD-SHOPELF.KC_STOCKCHANGED',
                'KC_StockChangeM-KC_StockChangeM-SHOPELF.KC_STOCKCHANGEM',
                'CG_StockOrderD-CG_StockOrderD-SHOPELF.CG_STOCKORDERD',
                'CG_StockOrderM-CG_StockOrderM-SHOPELF.CG_STOCKORDERM',
                'CG_StockInD-CG_StockInD-SHOPELF.CG_STOCKIND',
                'CG_StockInM-CG_StockInM-SHOPELF.CG_StockInM',
                'B_Supplier-B_Supplier-SHOPELF.B_Supplier',
                'B_StoreLocation-B_StoreLocation-SHOPELF.B_StoreLocation',
                'B_Store-B_Store-SHOPELF.B_Store',
                'B_Person-B_Person-SHOPELF.B_PERSON',
                'B_LogisticWay-B_LogisticWay-SHOPELF.B_LOGISTICWAY',
                'B_GoodsSKULocation-B_GoodsSKULocation-SHOPELF.B_GoodsSKULocation',
                'B_GoodsSKU-B_GoodsSKU-SHOPELF.B_GOODSSKU',
                'B_GoodsGroup-B_GoodsGroup-SHOPELF.B_GOODSGROUP',
                'B_Dictionary-B_Dictionary-SHOPELF.B_Dictionary',
                'B_Country-B_Country-SHOPELF.B_COUNTRY',
                'P_TradeStock-P_TradeStock-SHOPELF.P_TradeStock',
                'P_TradeDtStock-P_TradeDtStock-SHOPELF.P_TradeDtStock',
                'P_TradeDt-P_TradeDt-SHOPELF.P_TRADEDT',
                'P_Trade-P_Trade-SHOPELF.P_Trade'
                ];
    const pydb = "SHOPELF";
    const oadb = "magic";
    const dcdb = "DATACENTER_BUSINESS";
    ExecStatistics(ShopElfName, lines, pydb, dcdb);
}

function ExecStatistics(DBName, tables, sourceDB, targetDB) {
    var sourceSql = "";
    var targetSql = "";
    for (var index in tables) {
        var line = tables[index];
        var fields = splitString(line, "-");
        var cnName = fields[0];
        var sourceTableName = fields[1];
        var targetTableName = fields[2];
        if (isNotNull(sourceSql))
            sourceSql += " union ";
        sourceSql += "SELECT '" + cnName + "' AS 表名, count(1) as 记录数 from " + sourceTableName;
        if (isNotNull(targetSql))
            targetSql += " union ";
        targetSql += "SELECT '" + cnName + "' AS 表名, count(1) as 记录数 from " + targetTableName;
    }
    debug(sourceSql);
    debug(targetSql);
    var sourceMaps = MagicMapBySql(sourceDB, sourceSql, "表名");
    var targetMaps = MagicMapBySql(targetDB, targetSql, "表名");
    SaveSyncData(DBName, tables, sourceMaps, targetMaps);
}

//统计OA与数据中心数据差异
function ExecStatisticsForOA(){
    const DbName = "OA同步数据中心";
    const lines = ['tlk_gy_cg_03_pbm_rkd_detail-tlk_gy_cg_03_pbm_rkd_detail-voa.tlk_gy_cg_03_pbm_rkd_detail', 
                    'tlk_gy_cg_02_payment_sku-tlk_gy_cg_02_payment_sku-voa.tlk_gy_cg_02_payment_sku', 
                    'tlk_gy_cg_01_order_detail-tlk_gy_cg_01_order_detail-voa.tlk_gy_cg_01_order_detail', 
                    'tlk_gy_cg_02_payment-tlk_gy_cg_02_payment-voa.tlk_gy_cg_02_payment', 
                    'tlk_gy_cg_01_order_main-tlk_gy_cg_01_order_main-voa.tlk_gy_cg_01_order_main', 
                    'tlk_gy_cg_01_purchase_detail_source-tlk_gy_cg_01_purchase_detail_source-voa.tlk_gy_cg_01_purchase_detail_source', 
                    'tlk_gy_cg_01_purchase_detail_combo-tlk_gy_cg_01_purchase_detail_combo-voa.tlk_gy_cg_01_purchase_detail_combo', 
                    'tlk_gy_cg_01_purchase_detail-tlk_gy_cg_01_purchase_detail-voa.tlk_gy_cg_01_purchase_detail', 
                    'tlk_gy_cg_01_purchase_main-tlk_gy_cg_01_purchase_main-voa.tlk_gy_cg_01_purchase_main', 
                    'tlk_ywxp_02_new_arrivals-tlk_ywxp_02_new_arrivals-voa.tlk_ywxp_02_new_arrivals', 
                    'tlk_gy_st_03_assemblylist-tlk_gy_st_03_assemblylist-voa.tlk_gy_st_03_assemblylist', 
                    'tlk_gy_cg_02_payment_by_month_detail_logic-tlk_gy_cg_02_payment_by_month_detail_logic-voa.tlk_gy_cg_02_payment_by_month_detail_logic', 
                    'tlk_gy_cg_02_payment_by_month_logic_filing-tlk_gy_cg_02_payment_by_month_logic_filing-voa.tlk_gy_cg_02_payment_by_month_logic_filing', 
                    'tlk_报销登录表付款明细-tlk_报销登录表付款明细-voa.tlk_报销登录表付款明细', 
                    'tlk_应用服务申请单-tlk_应用服务申请单-voa.tlk_应用服务申请单', 
                    'tlk_公章或公司证件使用申请-tlk_公章或公司证件使用申请-voa.tlk_公章或公司证件使用申请', 
                    'tlk_公告通知-tlk_公告通知-voa.tlk_公告通知', 
                    'tlk_借款登记表-tlk_借款登记表-voa.tlk_借款登记表', 
                    'tlk_gy_st_03_assemblytask-tlk_gy_st_03_assemblytask-voa.tlk_gy_st_03_assemblytask',
                    'tlk_ywsta_01_main-tlk_ywsta_01_main-voa.tlk_ywsta_01_main', 
                    'tlk_ywsta_01_detail-tlk_ywsta_01_detail-voa.tlk_ywsta_01_detail', 
                    'tlk_ywsta_02_picking_goods-tlk_ywsta_02_picking_goods-voa.tlk_ywsta_02_picking_goods', 
                    'tlk_ywsta_03_replenish-tlk_ywsta_03_replenish-voa.tlk_ywsta_03_replenish', 
                    'tlk_ywsta_03_packing-tlk_ywsta_03_packing-voa.tlk_ywsta_03_packing'
                ];
    const oadb = "magic";
    const dcdb = "DATACENTER_BUSINESS";
    ExecStatistics(DbName, lines, oadb, dcdb);
}

function ExecStatistics(DBName, tables, sourceDB, targetDB) {
    var sourceSql = "";
    var targetSql = "";
    for (var index in tables) {
        var line = tables[index];
        var fields = splitString(line, "-");
        var cnName = fields[0];
        var sourceTableName = fields[1];
        var targetTableName = fields[2];
        if (isNotNull(sourceSql))
            sourceSql += " union ";
        sourceSql += "SELECT '" + cnName + "' AS 表名, count(1) as 记录数 from " + sourceTableName;
        if (isNotNull(targetSql))
            targetSql += " union ";
        targetSql += "SELECT '" + cnName + "' AS 表名, count(1) as 记录数 from " + targetTableName;
    }
    debug(sourceSql);
    debug(targetSql);
    var sourceMaps = MagicMapBySql(sourceDB, sourceSql, "表名");
    var targetMaps = MagicMapBySql(targetDB, targetSql, "表名");
    SaveSyncData(DBName, tables, sourceMaps, targetMaps);
}

function SaveSyncData(DBName, tables, sourceMaps, targetMaps) {
    const user = GetAssistant();
    const curDate = getToday();
    const applicationId = SysApplicationID();
    const docProcess = getDocProcess(applicationId);
    const billFormName = "DMPY_01_SyncToDataCenter";
    const formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
    const Form = formProcess.doViewByFormName(billFormName, applicationId);
    //清除数据
    var clearDataSql = "DELETE FROM tlk_dmpy_01_synctodatacenter WHERE ITEM_数据名称 = '" + DBName + "'";
    deleteByDSName("magic", clearDataSql);
    for (var index in tables) {
        var line = tables[index];
        var fields = splitString(line, "-");
        var cnName = fields[0];
        var sourceVo = sourceMaps.get(cnName);

        var sourceCount = sourceVo.get("记录数");
        if (isNotNull(sourceCount) == false)
            sourceCount = 0;
        var targetVo = targetMaps.get(cnName);

        var targetCount = targetVo.get("记录数");
        if (isNotNull(targetCount) == false)
            targetCount = 0;
        debug("sourceVo=" + sourceCount);
        debug("targetVo=" + targetCount);
        //创建表单                            
        var params = createParamsTable();
        var doc = docProcess.doNew(Form, user, params);
        doc.addStringItem("数据名称", DBName);
        doc.addStringItem("表名", cnName);
        doc.addDateItem("统计时间", curDate);
        doc.addDoubleItem("源记录数", sourceCount);
        doc.addDoubleItem("目标记录数", targetCount);
        docProcess.doCreate(doc);//创建   
    }
}
```

---

## 7. ChangeMoney

**模块说明：** 外汇金额转换工具函数集合，提供多币种之间的汇率转换功能。

**主要功能：**

- 外币转人民币汇率计算
- 多币种支持（USD、GBP等）
- 汇率换算处理
- 金额格式化

**数据来源：**

- 内置汇率配置
- 实时汇率数据（可扩展）

**使用场景：**

- 国际贸易金额计算
- 财务报表币种统一
- 采购订单金额转换
- 成本核算和分析
- 跨境电商结算

**注意事项：**

- 汇率数据需要定期更新
- 计算精度要求较高的场景需要使用实时汇率
- 不同币种的汇率波动影响
- 金额计算需要考虑舍入规则

**主要函数：**

```javascript
/**
 * 外币转换为人民币
 * @param {string} MoneyType - 币种（USD、GBP等）
 * @param {number} Amount - 金额
 * @returns {number} 兑换后的人民币金额
 */
function ChangeToRMB(MoneyType, Amount){
  var rate = 1;
  if ("USD" == MoneyType || "GBP" == MoneyType)
    rate = 7;
  return Amount * rate;
}
```

---

## 8. PDIUtils

**模块说明：** 与PDI（Pentaho Data Integration）服务交互的函数集合，负责数据导入、转换、同步等ETL操作。

**主要功能：**

- PDI任务调度和执行
- 数据导入和同步处理
- 任务状态监控和管理
- 业务流程数据处理
- 采购数据计算和分析
- 海外仓发货数据处理
- 装箱证明生成

**数据来源：**

- PDI服务器 (***********:8080)
- DATACENTER_BUSINESS: 业务数据
- magic: 任务状态数据
- 各种业务系统数据源

**使用场景：**

- 大批量数据导入和处理
- 跨系统数据同步
- 复杂业务计算
- 定时数据处理任务
- 报表数据准备
- 数据质量检查

**注意事项：**

- PDI服务器连接稳定性
- 任务执行时间较长，需要异步处理
- 任务状态需要实时监控
- 失败任务需要重试机制
- 大数据量处理注意内存使用
- 任务并发控制避免资源冲突

**主要函数：**

```javascript
/**
 * 在STA发货流程的备货流程中导入SKUList
 * @param {string} account - 账号
 * @param {string} site - 站点
 * @param {string} billNumber - 单据编号
 * @param {string} stateLabel - 状态标签
 */
function ImportSKUListFromSTA(account, site, billNumber, stateLabel) {
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址       
  var requestMethod = "POST";//请求方式（GET、POST）       
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/WAREHOUSE_SHIPMENTITEMS/JOB_INSERT_PRINTLABEL_SKULIST.kjb\"," +
    "\"jobName\":\"JOB_INSERT_PRINTLABEL_SKULIST\", \"outputFile\":\"/home/<USER>"," +
    "\"runInBackground\":true, \"jobParameters\":[{\"name\":\"ACCOUNT\", \"stringValue\":[\"" + account +
    "\"], \"type\":\"string\"},{\"name\":\"SITE\", \"stringValue\":[\"" + site +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"STATELABEL\", \"stringValue\":[\"" + stateLabel +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"}," +
    "{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  var contentType = "application/json";//请求内容类型       
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //heads.put("content-Type",contentType);            
  //接口返回内容      
  RequestPDI("JOB_INSERT_PRINTLABEL_SKULIST", false, billNumber, outputStr); 
  try {
    //URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);

  } catch (e) {

  }
}

/**
 * 在STA发货流程的备货流程中补录ALLSKU
 */
function RecordAllSKUFromSTA() {
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址       
  var requestMethod = "POST";//请求方式（GET、POST）       
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/WAREHOUSE_SHIPMENTITEMS/CT_PRINTLABEL_ALLSKU.ktr\", \"appendDateFormat\":null, \"overwriteFile\":\"false\", \"jobName\":\"CT_PRINTLABEL_ALLSKU\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";

  var contentType = "application/json";//请求内容类型       
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);

  //接口返回内容       
  try {
    URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
  } catch (e) {

  }
}

/**
 * 同步普源数据
 * @param {string} billNumber - 单据编号
 * @param {string} refereneDay - 参考日期
 * @param {string} startDay - 开始日期
 * @param {string} endDay - 结束日期
 */
function SyncShopElfData(billNumber, refereneDay, startDay, endDay) {
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  var requestMethod = "POST";//请求方式（GET、POST）  
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/SHOPELF_CG_PREPOSITION/JOB_SHOPELF_CG_PREPOSITION-V20241029.kjb\", \"jobName\":\"JOB_SHOPELF_CG_PREPOSITION-V20241029\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"},{\"name\":\"STARTDATE\", \"stringValue\":[\"" + refereneDay + "\"], \"type\":\"string\"},{\"name\":\"FROMDATE\", \"stringValue\":[\"" + startDay + "\"], \"type\":\"string\"},{\"name\":\"TODATE\", \"stringValue\":[\"" + endDay + "\"], \"type\":\"string\"}]}";
  var contentType = "application/json";//请求内容类型  
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //接口返回内容  
  try {
    URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
  } catch (e) {

  }
}

/**
 * 请求生成索赔用装箱证明
 * @param {string} billNumber - 单据编号
 */
function GeneratePackingProofForClaims(billNumber) {
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  var requestMethod = "POST";//请求方式（GET、POST）  
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/WAREHOUSE_SHIPMENTITEMS/JOB_AM_SHIPMENTS_PACKINGLIST.kjb\", \"jobName\":\"JOB_AM_SHIPMENTS_PACKINGLIST\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  var contentType = "application/json";//请求内容类型  
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //接口返回内容  
  try {
    URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
  } catch (e) {

  }
}

/**
 * 判断PDI任务是否完成
 * @param {string} taskName - 任务名称
 * @returns {boolean} true表示任务已完成
 */
function PDITaskIsEnd(taskName) {
  var taskStatus = GetPDITask(taskName);
  if (taskStatus == null)
    return true;
  var startTime = taskStatus.getItemValueAsDate("运行时间");
  var startTimeStr = format(startTime, "yyyy-MM-dd HH:mm:ss");
  var sql = "select STATUS, ENDDATE from BUSINESS.PDI_ALLJOB_LOGS " +
    "where JOBNAME = '" + taskName + "' " +
    "AND ENDDATE >= '" + startTimeStr + "' " +
    "order by ID_JOB DESC limit 1";
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  if (data == null) {
    //任务创建中
    UpdatePDITaskStatus(taskName, "准备运行");
    return false;
  } else {
    if ("end".equals(data.get("STATUS"))) {
      return true;
    } else {
      UpdatePDITaskStatus(taskName, "运行中");
      return false;
    }
  }
}

/**
 * 检查计算采购金额任务状态
 * @returns {boolean} true表示任务已完成
 */
function CheckCalcCGAmountTaskState() {
  var jobName = "JOB_SHOPELF_STOCKS_PURCHASE_ALL_FOR_OASTA";
  var isEnd = PDITaskIsEnd(jobName);
  if (isEnd == false)
    return false;
  //检查结果表里有没有数据，如果有说明还没有入库，任务还没有结束
  var sql = "SELECT COUNT(1) AS rowNum FROM PDI_SHOPELF_STOCKS_PURCHASE_TEST";
  var data = MagicFindBySql("DATACENTER_BUSINESS", sql);
  var result = data.get("rowNum");
  if (result > 0) {
    //还没入库
    UpdatePDITaskStatus(jobName, "数据入库");
    return false;
  } else {
    //完成
    DeletePDITaskStatus(jobName);
    return true;
  }
}

/**
 * 按STA发货需求计算采购量
 * @param {string} billNumber - 单据编号
 * @param {Object} user - 用户对象
 */
function CalcCGAmount(billNumber, user) {
  //加锁
  var jobName = "JOB_SHOPELF_STOCKS_PURCHASE_ALL_FOR_OASTA";
  AddPDITaskStatus(jobName, billNumber, user, "启动");
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  var requestMethod = "POST";
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/SHOPELF_CG_PREPOSITION/" + jobName + ".kjb\", \"jobName\":\"" + jobName + "\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  println("outputStr=" + outputStr);
  var contentType = "application/json";//请求内容类型  
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //接口返回内容  
  var isSuccess = false;
  try {
    var temp = "" + URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
    var json = temp.toJSON();
    var jsonObject = Packages.net.sf.json.JSONObject.fromObject(json);
    var status = jsonObject.get("Status");
    if ("401".equals(status)) {
      debugm(billNumber + "PDIUtils.CalcCGAmount按STA发货需求计算采购量时，ProductToMaterials请求失败，重新发起请求。");
      //解锁
      DeletePDITaskStatus(jobName);
      CalcCGAmount(billNumber, user);
    }
  } catch (e) {
    errorm("PDIUtils.CalcCGAmount按STA发货需求计算采购量时，发现异常：" + e.message);
  }
}

//添加PDI任务运行状态
function AddPDITaskStatus(jobName, billNumber, user, status) {
  var applicationId = getApplication();//获取applicationId                            
  var docProcess = getDocProcess(applicationId);
  var billFormName = "SY_TS_PDI_TASK_STATUS";
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
  var Form = formProcess.doViewByFormName(billFormName, applicationId);
  var params = createParamsTable();
  var doc = docProcess.doNew(Form, user, params);
  doc.addStringItem("相关单号", billNumber);
  doc.addStringItem("任务名称", jobName);
  doc.addDateItem("运行时间", getToday());
  doc.addStringItem("状态", status);
  doc.addStringItem("执行人", user.getName());
  docProcess.doCreate(doc);
}

function UpdatePDITaskStatus(jobName, status) {
  var sql = "Update tlk_sy_ts_pdi_task_status " +
    "SET ITEM_状态 = '" + status + "' " +
    "WHERE ITEM_任务名称='" + jobName + "'";
  updateByDSName("magic", sql);
}

function DeletePDITaskStatus(jobName) {
  try {
    var sql = "DELETE FROM tlk_sy_ts_pdi_task_status " +
      "WHERE ITEM_任务名称='" + jobName + "'";
    deleteByDSName("magic", sql);
  } catch (e) {

  }

}

function GetPDITask(jobName) {
  var sql = "select * from tlk_sy_ts_pdi_task_status where ITEM_任务名称 = '" + jobName + "'";
  var data = MagicFindBySql("magic", sql);
  if (isNotNull(data)) {
    return findDocument(data.get("ID"));
  } else
    return null;
}

function CheckPDITaskIsEnd(jobName) {
  var sql = "select * from tlk_sy_ts_pdi_task_status where ITEM_任务名称 = '" + jobName + "'";
  var data = MagicFindBySql("magic", sql);
  return isNotNull(data) == false;
}

function PDITaskIsEnd(jobName) {
  var job = GetPDITask(jobName);
  return isNotNull(job) == false;
}

//OA海外仓发货备货流程，审定过程获取采购在途及到货情况
function ShopElfStocksReceived(billNumber) {
  //加锁
  var jobName = "JOB_PBI_SHOPELF_STOCKS_RECEIVED";
  var user = getWebUser();
  AddPDITaskStatus(jobName, billNumber, user, "启动");
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  var requestMethod = "POST";
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/OTHERS/" + jobName + ".kjb\", \"jobName\":\"" + jobName + "\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  println("outputStr=" + outputStr);
  var contentType = "application/json";//请求内容类型  
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //接口返回内容  
  //try {
  //  URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
  //} catch (e) {
  //
  //}
  RequestPDI(jobName, true, billNumber, outputStr);
}

//采购申请成品拆分物料
function ProductToMaterials(billNumber) {
  //var jobName = "JOB_OAFLOW_PURCHASE_DETAILS";
  var jobName = "JOB_OAFLOW_PURCHASE_DETAILS_V20250815";
  var user = getWebUser();
  AddPDITaskStatus(jobName, billNumber, user, "启动");
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  var requestMethod = "POST";
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/CT_OAFLOW_PURCHASE_DETAILS/" + jobName + ".kjb\", \"jobName\":\"" + jobName + "\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  var contentType = "application/json";//请求内容类型  
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //接口返回内容  
  var isSuccess = false;
  try {
    var temp = "" + URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
    var json = temp.toJSON();
    var jsonObject = Packages.net.sf.json.JSONObject.fromObject(json);
    var status = jsonObject.get("Status");
    if ("401".equals(status)) {
      debugm("ProductToMaterials请求失败，重新发起请求。");
      //解锁
      DeletePDITaskStatus(jobName);
      ProductToMaterials(billNumber);
    }
  } catch (e) {
    errorm("采购申请成品拆分物料请求PDI时出错：" + e.message);
  }
}

//导入海外仓发货审批表
function PDIImportRequirementDetail(doc, user) {
  var docId = doc.getId();
  var jobName = "JOB_YWREQ_REQUIREMENT_DETAIL_IMPORT";
  var billNumber = doc.getId();
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/Magic5_VOA/" + jobName + ".kjb\", \"jobName\":\"" + jobName + "\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + docId + "\"], \"type\":\"string\"},{\"name\":\"DIRECTOR\", \"stringValue\":[\"" + user.getName() + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  RequestPDI(jobName, false, billNumber, outputStr);

  // var jobName = "JOB_YWREQ_REQUIREMENT_DETAIL_IMPORT";
  // var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  // var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  // var requestMethod = "POST";
  // var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/Magic5_VOA/" + jobName + ".kjb\", \"jobName\":\"" + jobName + "\", \"outputFile\":\"/home/<USER>", \"runInBackground\":true, \"jobParameters\":[{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + docId + "\"], \"type\":\"string\"},{\"name\":\"DIRECTOR\", \"stringValue\":[\"" + user.getName() + "\"], \"type\":\"string\"},{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"},{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  // var contentType = "application/json";//请求内容类型  
  // var auth = "YWRtaW46cGFzc3dvcmQ=";
  // var heads = createObject("java.util.HashMap");
  // heads.put("Authorization", "Basic " + auth);
  // //接口返回内容  
  // var isSuccess = false;
  // try {
  //   debug("PDI请求：" + outputStr);
  //   var temp = "" + URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
  //   var json = temp.toJSON();
  //   var jsonObject = Packages.net.sf.json.JSONObject.fromObject(json);
  //   var status = jsonObject.get("Status");
  //   if ("401".equals(status)) {
  //     debug(doc, user, doc.getItemValueAsString("编号") + "PDIImportRequirementDetail请求失败，重新发起请求。");
  //     PDIImportRequirementDetail(doc, user);
  //   }
  // } catch (e) {
  //   error(doc, user, "导入海外仓发货审批表请求PDI时出错：" + e.message);
  // }
  // info(doc, user, "PDIImportRequirementDetail请求成功！");
}


//试算发货需求
function TrialShipmentRequirement(account, site, dateRange, billNumber) {
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/WAREHOUSE_SHIPMENTITEMS/JOB_WAREHOURS_SHIPMENTITEMS.kjb\"," +
    "\"jobName\":\"JOB_WAREHOURS_SHIPMENTITEMS\", \"outputFile\":\"/home/<USER>"," +
    "\"runInBackground\":true, \"jobParameters\":[{\"name\":\"ACCOUNT\", \"stringValue\":[\"" + account +
    "\"], \"type\":\"string\"},{\"name\":\"DATE_RANGE\", \"stringValue\":[\"" + dateRange + "\"]," +
    "\"type\":\"string\"},{\"name\":\"SITE\", \"stringValue\":[\"" + site +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"}," +
    "{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  RequestPDI("JOB_WAREHOURS_SHIPMENTITEMS", false, billNumber, outputStr);
}

function JOB_OASTA_PACKING_DECLARED_VALUES(account, site, billNumber){
  var outputStr = "{\"inputFile\":\"/home/<USER>/LJC/Magic5_VOA/JOB_OASTA_PACKING_DECLARED_VALUES.kjb\"," +
    "\"jobName\":\"JOB_OASTA_PACKING_DECLARED_VALUES\", \"outputFile\":\"/home/<USER>"," +
    "\"runInBackground\":true, \"jobParameters\":[{\"name\":\"ACCOUNT\", \"stringValue\":[\"" + account +
    "\"], \"type\":\"string\"},{\"name\":\"SITE\", \"stringValue\":[\"" + site +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"BILLNUMBER\", \"stringValue\":[\"" + billNumber +
    "\"], \"type\":\"string\"}," +
    "{\"name\":\"accepted-page\", \"stringValue\":[\"0\"], \"type\":\"number\"}," +
    "{\"name\":\"autoSubmit\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"autoSubmitUI\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"showParameters\", \"stringValue\":[\"true\"], \"type\":\"string\"},{\"name\":\"appendDateFormat\", \"stringValue\":[\"ul\"], \"type\":\"string\"}]}";
  RequestPDI("JOB_OASTA_PACKING_DECLARED_VALUES", false, billNumber, outputStr);
}


function RequestPDI(jobName, isLock, billNumber, outputStr) {
  var user = getWebUser();
  if (isLock)
    AddPDITaskStatus(jobName, billNumber, user, "启动");
  debug("开始向PDI请求，jobName = " + jobName);
  var URLConnector = new Packages.cn.myapps.util.http.HttpRequestUtil();
  var requestUrl = "http://***********:8080/pentaho/api/scheduler/job";//请求接口地址  
  debug("reequestUrl=" + requestUrl);
  var requestMethod = "POST";
  debug("requestMethod=" + requestMethod);
  debug("outputStr=" + outputStr);
  var contentType = "application/json";//请求内容类型  
  debug("contentType=" + contentType);
  var auth = "YWRtaW46cGFzc3dvcmQ=";
  debug("auth=" + auth);
  var heads = createObject("java.util.HashMap");
  heads.put("Authorization", "Basic " + auth);
  //接口返回内容  
  var isSuccess = false;
  var temp = "" + URLConnector.request(requestUrl, requestMethod, outputStr, contentType, heads);
  var json = temp.toJSON();
  var jsonObject = Packages.net.sf.json.JSONObject.fromObject(json);
  var status = jsonObject.get("Status");
  if ("200".equals(status) == false)
    debugm(billNumber + "请求PDI." + jobName + " : " + status + " 返回结果：" + jsonObject);
  if ("401".equals(status)) {
    debugm("ProductToMaterials请求失败，重新发起请求。");
    //解锁
    if (isLock)
      DeletePDITaskStatus(jobName);
    RequestPDI(jobName, isLock, billNumber, outputStr);
  }
  //解锁
  if (isLock)
    DeletePDITaskStatus(jobName);
}
```

---

## 9. FinancialUtils

**模块说明：** 财务相关工具函数集合，处理信用卡管理、发票校验、报销计算等财务业务。

**主要功能：**

- 信用卡信息管理和查询
- 发票文件名格式校验
- 发票重复报销检查
- 报销金额计算和汇总
- 头程费用计算
- 财务数据格式化

**数据来源：**

- magic: 信用卡和报销数据
- 发票文件系统
- 报销明细数据

**使用场景：**

- 员工报销申请处理
- 信用卡费用管理
- 发票合规性检查
- 财务审核流程
- 费用统计和分析
- 国际物流费用计算

**注意事项：**

- 发票编号唯一性严格校验
- 金额计算精度要求高
- 文件名格式标准化
- 重复报销防护机制
- 用户权限验证
- 数据备份和恢复

**主要函数：**

```javascript
/**
 * 获取有效的信用卡列表
 * @param {Object} user - 用户对象
 * @returns {Object} 信用卡选项列表
 */
function GetCreditCards(user){
  var sql = "SELECT * FROM tlk_fn_03_credit_card_manage WHERE ITEM_状态 = '正常' AND ITEM_授权用户 LIKE '%" + user.getId() + "%'";
  var arr = MagicQueryBySql("magic", sql);
  var opts = $TOOLS.createOptions(); //创建指定类对象
  opts.add("", "");
  if (arr != null && arr.size() > 0){
    for(var j =0; j < arr.size(); j++){
      var vo = arr.get(j);
      var cardName = vo.get("ITEM_信用卡显示名称") + "（" + vo.get("ITEM_银行账号") + "）";
      opts.add(cardName, vo.get("ID")); 
    }
  }
  return opts;
}

/**
 * 校验发票文件名称格式是否正确
 * 格式要求：8位日期-金额-发票编号-序号.pdf
 * @param {string} fileName - 发票文件名
 * @returns {string} 校验结果，空字符串表示格式正确
 */
function ValidFaPiaoFileName(fileName){
  var arr = splitString(fileName, "-");
  if (arr.length != 4)
    return fileName + "发票名称格式不正确,参考格式:8位日期-金额-发票编号-序号.pdf";
  var datePart = arr[0];
  if (datePart.length() != 8)
    return fileName + "发票名称格式不正确,第一部份的日期长度必须是8位" + datePart.length();
  var faPiaoNo = arr[2];
  if (faPiaoNo.length() <= 4)
    return fileName + "发票名称格式不正确，发票编号长度太短。";
  var uniquePart = FaPiaoUnique(faPiaoNo);
  if (isNotNull(uniquePart))
    return uniquePart;
  return "";
}

/**
 * 检验发票是否已报销（唯一性校验）
 * @param {string} faPiaoNo - 发票编号
 * @returns {string} 校验结果，空字符串表示可以报销
 */
function FaPiaoUnique(faPiaoNo){
  if (isNotNull(faPiaoNo) == false)
    return "进行发票唯一校验时，发票编号不能为空";
  var domainID = SysDomainID();
  var sql = "select * from tlk_报销登记表 WHERE STATELABEL <> '作废' AND ITEM_上传发票 LIKE '%-" + faPiaoNo + "-%'";
  var t_count = countBySQL(sql);
  if (t_count > 1)
    return "发票：" + faPiaoNo + "已报销，同一张发票不能重复报销!";
  if (t_count == 1){
     var query = queryBySQL(sql);  
    var result = 0;
    if (query != null){
      for (var iter = query.iterator(); iter.hasNext();) { 
        var data = iter.next(); 
        var fp = data.getItemValueAsString("上传发票"); 
        var json = JSON.parse(fp);
        for(var i = 0; i < json.length; i++){
          println("json =" + json);
          var fileName = json[i].name;
          if (fileName.indexOf(faPiaoNo) > 0){
            result += 1;
          }
        }
      } 
      if (result > 1)
        return "发票：" + faPiaoNo + "已报销，同一张发票不能重复报销。";
    }  
  }
}

/**
 * 汇总报销单明细金额
 * @param {Object} doc - 报销单文档对象
 * @returns {string} 格式化后的金额
 */
function GetPayMoney(doc) {
  var kind = doc.getItemValueAsString("费用类别");
  if ("国际物流费用-头程费用".equals(kind))
    return GetFirstLegMoney();
  else
    return GetDetailMoney();
}

/**
 * 获取报销明细金额合计
 * @returns {string} 格式化后的金额
 */
function GetDetailMoney() {
  var formName = "报销登记表明细";
  var fieldName = "金额";
  var payMoney = sumSubDocument(formName, fieldName);
  var applyMoney = getItemValueAsDouble("经费申请费用");
  var applyBillNo = getItemValueAsString("采购申请单编号");
  var formatter = createObject("java.text.DecimalFormat");
  formatter.applyLocalizedPattern("0.00");
  return formatter.format(payMoney);
}

/**
 * 头程费用合计
 * @returns {string} 格式化后的头程费用金额
 */
function GetFirstLegMoney() {
  var doc = getCurrentDocument();
  var sql = "SELECT SUM(ITEM_单价 * ITEM_重量) AS ITEM_AMOUNT FROM tlk_头程费用报销登记表 WHERE PARENT = '" + doc.getId() + "'";
  var payMoney = sumBySQL(sql, "AMOUNT");
  var applyMoney = getItemValueAsDouble("经费申请费用");
  var applyBillNo = getItemValueAsString("采购申请单编号");
  var formatter = createObject("java.text.DecimalFormat");
  formatter.applyLocalizedPattern("0.00");
  return formatter.format(payMoney);
}
```

---

## 10. ITUtils

**模块说明：** IT系统维护相关函数集合，主要处理IT巡检任务的自动化生成和管理。

**主要功能：**

- IT巡检任务自动生成
- 巡检模板管理
- 巡检流程启动
- 巡检明细创建
- IT维护任务调度
- 系统状态监控

**数据来源：**

- magic: 巡检任务和模板数据
- IT巡检配置表
- 系统运行状态数据

**使用场景：**

- 定期IT系统巡检
- 系统维护任务管理
- IT服务质量监控
- 故障预防和检查
- 运维流程自动化
- IT资源管理

**注意事项：**

- 巡检任务定时执行
- 模板配置需要及时更新
- 巡检结果需要及时处理
- 异常情况需要立即响应
- 权限控制确保安全
- 巡检记录需要归档保存

**主要函数：**

```javascript
/**
 * 生成IT巡检任务
 * 自动创建IT巡检主单和明细，并启动流程
 */
function ITGenerateInspection(){
  var userName = ITManager();
  var userId = getUserIdByUserName(userName);
  var user = getUserById(userId);
  var applicationId = SysApplicationID();
  var docProcess = getDocProcess(applicationId);                            
  var orderMainDoc;                                
  //创建采购订单                            
  var cgdPrefix="ITIM-";                              
  var cgBillNo = countNext2(cgdPrefix,true,true,true,4);                             
  var billFormName = "ITIN_Inspection_Main";                            
  var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();                            
  var Form = formProcess.doViewByFormName(billFormName, applicationId);
  var params = createParamsTable();                            
  var doc = docProcess.doNew(Form, user, params);                            
  doc.addStringItem("编号",cgBillNo);                            
  doc.addStringItem("填写人", user.getName());
  doc.addDateItem("填写日期", getToday());
  doc.addStringItem("是否存在异常", "否");

  docProcess.doCreate(doc);//创建                            
  //重新获取采购订单对象                            
  doc = LoadByIndexFieldValue("tlk_itin_inspection_main", "ITEM_编号", cgBillNo, true);                              
  var flowid = "__B1N6w747Fqdym2hM3BP";                      
  var billParams = createParamsTable();                                           
  billParams.setParameter("_flowid", flowid);                            
  docProcess.doStartFlowOrUpdate(doc, billParams, user); //启动流程  

  var detailFormName = "ITIN_Inspection_Detail";      
  var detailForm = formProcess.doViewByFormName(detailFormName, applicationId);
  var detailParams = createParamsTable();   
  var detailSql = "select * from tlk_itin_inspection_template";
   var query = queryBySQL(detailSql);  
    if (query != null){  
      for (var iter = query.iterator(); iter.hasNext();) { 
        var data = iter.next(); 
        var detailDoc = docProcess.doNew(detailForm, user, detailParams);     
        detailDoc.addStringItem("序号", data.getItemValueAsString("序号"));
        detailDoc.addStringItem("巡检类型", data.getItemValueAsString("巡检类型"));
        detailDoc.addStringItem("重要等级", data.getItemValueAsString("重要等级"));
        detailDoc.addStringItem("任务名称", data.getItemValueAsString("任务名称"));
        detailDoc.addStringItem("巡检工作内容", data.getItemValueAsString("巡检工作内容"));
        detailDoc.addStringItem("巡检结果", data.getItemValueAsString("巡检结果"));
        detailDoc.setParent(doc.getId());
        docProcess.doCreate(detailDoc);//创建     
      } 
    }  
}
```

---