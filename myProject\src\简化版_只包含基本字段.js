#include "SysUserUtils";
#include "DBUtils";

// 简化版 - 只包含确定存在的字段
try {
    var currentDocId = this.getId();
    debug("开始处理文档ID: " + currentDocId);
    
    if (currentDocId) {
        // 查询当前文档数据
        var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
        var sourceData = MagicFindBySql("magic", sql);
        
        if (sourceData) {
            debug("查询到源数据");
            
            // 获取系统对象
            var user = GetAssistant();
            var applicationId = getApplication();
            var docProcess = getDocProcess(applicationId);
            
            // 生成编号
            var billNo = countNext2("PSMT-", true, true, true, 4);
            debug("生成编号: " + billNo);
            
            // 获取目标表单 - 尝试多种可能的表单名称
            var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
            var possibleFormNames = [
                "tlk_psm_01_personal_affairs_management_list",
                "PSM_01_Personal_Affairs_Management_List",
                "个人事务提醒列表",
                "personal_affairs_management_list",
                "psm_01_list"
            ];

            var listForm = null;
            var actualFormName = "";

            for (var i = 0; i < possibleFormNames.length; i++) {
                try {
                    listForm = formProcess.doViewByFormName(possibleFormNames[i], applicationId);
                    if (listForm) {
                        actualFormName = possibleFormNames[i];
                        debug("找到目标表单: " + actualFormName);
                        break;
                    }
                } catch (e) {
                    debug("尝试表单名称失败: " + possibleFormNames[i]);
                }
            }

            if (listForm) {
                debug("找到目标表单");
                
                // 创建新记录
                var params = createParamsTable();
                var listDoc = docProcess.doNew(listForm, user, params);
                
                // 只设置确定存在的字段
                listDoc.addStringItem("编号", billNo);
                debug("设置编号: " + billNo);
                
                // 事务标题 - 尝试多种可能的字段名
                var title = sourceData.get("ITEM_事务标题") || 
                           sourceData.get("ITEM_标题") || 
                           sourceData.get("ITEM_事务名称") || 
                           sourceData.get("ITEM_名称") || "个人事务";
                listDoc.addStringItem("事务标题", title);
                debug("设置事务标题: " + title);
                
                // 内容 - 尝试多种可能的字段名
                var content = sourceData.get("ITEM_事务描述") || 
                             sourceData.get("ITEM_备注") || 
                             sourceData.get("ITEM_内容") || 
                             sourceData.get("ITEM_说明") || 
                             sourceData.get("ITEM_描述") || "";
                listDoc.addStringItem("内容", content);
                debug("设置内容: " + content);
                
                // 合规提醒 - 固定值
                listDoc.addStringItem("合规提醒", "是");
                debug("设置合规提醒: 是");
                
                // 创建记录
                docProcess.doCreate(listDoc);
                debug("成功创建提醒记录: " + billNo);
                
            } else {
                debug("未找到任何匹配的目标表单，尝试的表单名称:");
                for (var j = 0; j < possibleFormNames.length; j++) {
                    debug("  - " + possibleFormNames[j]);
                }
            }
        } else {
            debug("未找到源数据，SQL: " + sql);
        }
    } else {
        debug("无法获取文档ID");
    }
} catch (e) {
    debug("创建提醒记录失败: " + e.message);
    debug("错误详情: " + e);
}
