# 个人事务管理自动提醒功能实现方案

## 方案概述

基于您提供的参考脚本，我为您实现了一个完整的个人事务管理自动提醒功能。当用户编辑保存 `tlk_psm_01_personal_affairs_management` 表的内容时，系统会自动在 `tlk_psm_01_personal_affairs_management_list` 表中创建对应的事务提醒记录。

## 文件结构

```
myProject/src/
├── personal_affairs_auto_reminder.js      # 完整的自动提醒实现
├── personal_affairs_trigger.js            # 表单保存事件触发器
├── 简化版本_表单保存事件.js                # 直接嵌入表单的简化版本
├── 测试脚本.js                            # 功能测试脚本
├── 个人事务自动提醒配置说明.md             # 详细配置说明
└── 实现方案总结.md                        # 本文档
```

## 核心功能特性

### 1. 自动触发机制
- 在表单保存事件中自动触发
- 支持工作流节点事件触发
- 可手动批量创建提醒记录

### 2. 智能字段映射
- 自动映射源表字段到提醒表字段
- 支持多种字段名称的兼容性处理
- 安全的字段值获取机制

### 3. 重复创建防护
- 基于关联事务ID检查是否已存在提醒记录
- 避免重复创建相同的提醒记录

### 4. 灵活的提醒时间设置
- 根据计划完成时间自动计算提醒时间
- 支持自定义提醒提前天数
- 默认提醒时间设置

### 5. 完善的错误处理
- 全面的异常捕获和处理
- 详细的调试日志输出
- 错误不影响原始保存操作

## 推荐使用方案

### 方案一：简化版本（推荐）

**适用场景**: 直接在表单保存事件中使用

**使用步骤**:
1. 打开个人事务管理表单设计器
2. 找到"保存后事件"配置
3. 将 `简化版本_表单保存事件.js` 中的代码复制到事件中
4. 保存表单配置

**优点**:
- 实现简单，无需额外文件
- 代码集中，易于维护
- 直接嵌入，执行效率高

### 方案二：模块化版本

**适用场景**: 需要在多个地方调用，或需要更灵活的控制

**使用步骤**:
1. 将 `personal_affairs_trigger.js` 部署到系统脚本目录
2. 在表单保存事件中添加：
```javascript
#include "personal_affairs_trigger.js";
onPersonalAffairsSave(doc);
```

**优点**:
- 代码复用性好
- 便于统一维护和升级
- 支持多种触发方式

## 字段映射说明

### 自动映射的字段

| 源表字段候选 | 目标字段 | 默认值 |
|-------------|---------|--------|
| 事务标题/标题/事务名称 | 事务标题 | "个人事务提醒" |
| 事务类型 | 事务类型 | "一般事务" |
| 优先级 | 优先级 | "普通" |
| 事务描述/备注/说明 | 事务描述 | "" |
| 负责人/经办人/处理人 | 负责人 | - |
| 部门/所属部门 | 部门 | - |
| 计划完成时间/截止时间/完成日期 | 计划完成时间 | - |

### 自动生成的字段

| 字段 | 生成规则 |
|------|---------|
| 编号 | PSMT-XXXX（自动递增） |
| 创建人 | 当前用户姓名 |
| 创建日期 | 当前日期 |
| 关联事务ID | 源文档ID |
| 关联事务编号 | 源文档编号 |
| 提醒时间 | 计划完成时间-1天（或明天） |
| 提醒状态 | "待提醒" |
| 是否已提醒 | "否" |
| 提醒方式 | "系统通知" |
| 提醒次数 | 0 |

## 部署步骤

### 1. 准备工作
- 确认数据库表结构正确
- 确认用户权限配置
- 备份相关表单配置

### 2. 部署脚本
选择以下方式之一：

**方式A: 简化版本**
```javascript
// 直接复制到表单保存后事件中
#include "SysUserUtils";
// ... 完整代码见 简化版本_表单保存事件.js
```

**方式B: 模块化版本**
```javascript
// 在表单保存后事件中
#include "personal_affairs_trigger.js";
onPersonalAffairsSave(doc);
```

### 3. 测试验证
```javascript
// 运行测试脚本
#include "测试脚本.js";
testPersonalAffairsReminder();
```

### 4. 生产部署
- 在测试环境验证功能正常
- 部署到生产环境
- 监控运行状况

## 扩展功能

### 1. 自定义提醒规则
```javascript
// 根据优先级设置不同的提醒时间
var daysBefore = (priority === "紧急") ? 1 : (priority === "重要") ? 2 : 3;
var reminderDate = adjustDay(planDate, -daysBefore);
```

### 2. 条件化创建
```javascript
// 只为特定类型的事务创建提醒
var type = safeGetString(doc, "事务类型");
if (type === "重要事务" || type === "紧急事务") {
    // 创建提醒记录
}
```

### 3. 多种提醒方式
```javascript
// 根据用户偏好设置提醒方式
var userPreference = getUserReminderPreference(responsible);
reminderDoc.addStringItem("提醒方式", userPreference);
```

### 4. 批量处理
```javascript
// 为现有事务批量创建提醒
function batchCreateReminders() {
    var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management " +
             "WHERE ID NOT IN (SELECT ITEM_关联事务ID FROM tlk_psm_01_personal_affairs_management_list)";
    // 处理逻辑...
}
```

## 监控和维护

### 1. 日志监控
- 定期检查系统日志中的错误信息
- 监控提醒记录创建成功率
- 关注性能指标

### 2. 数据维护
```sql
-- 检查提醒记录统计
SELECT 
    COUNT(*) as 总提醒数,
    SUM(CASE WHEN ITEM_提醒状态 = '待提醒' THEN 1 ELSE 0 END) as 待提醒数,
    SUM(CASE WHEN ITEM_是否已提醒 = '是' THEN 1 ELSE 0 END) as 已提醒数
FROM tlk_psm_01_personal_affairs_management_list;

-- 检查关联关系完整性
SELECT COUNT(*) as 孤立提醒记录数
FROM tlk_psm_01_personal_affairs_management_list l
LEFT JOIN tlk_psm_01_personal_affairs_management m ON l.ITEM_关联事务ID = m.ID
WHERE m.ID IS NULL;
```

### 3. 性能优化
- 为关联字段创建数据库索引
- 定期清理过期的提醒记录
- 优化查询语句

## 故障排除

### 常见问题及解决方案

1. **提醒记录创建失败**
   - 检查表单名称和字段名称
   - 验证用户权限
   - 查看系统日志

2. **字段值映射错误**
   - 确认源表字段名称
   - 检查字段类型匹配
   - 使用安全获取函数

3. **重复创建问题**
   - 检查重复性检查逻辑
   - 确认关联字段设置
   - 验证数据库约束

4. **性能问题**
   - 添加数据库索引
   - 优化查询条件
   - 考虑异步处理

## 总结

本实现方案提供了一个完整、可靠、易于维护的个人事务自动提醒功能。通过合理的架构设计和完善的错误处理，确保了功能的稳定性和可扩展性。建议根据实际需求选择合适的部署方案，并在生产环境中进行充分的测试验证。
