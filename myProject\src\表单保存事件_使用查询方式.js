#include "SysUserUtils";
#include "DBUtils";

// 在表单保存后事件中使用
try {
    // 获取当前保存的文档ID
    var currentDocId = this.getId(); // 或者使用其他方式获取文档ID

    // 1. 查询当前文档的数据
    var sql = "SELECT * FROM tlk_psm_01_personal_affairs_management WHERE ID = '" + currentDocId + "'";
    var sourceData = MagicFindBySql("magic", sql);

    if (sourceData) {
        debug("查询到源数据，文档ID: " + currentDocId);

        // 2. 检查是否已存在提醒记录（避免重复创建）
        var checkSql = "SELECT COUNT(*) as count FROM tlk_psm_01_personal_affairs_management_list " +
                      "WHERE ITEM_关联事务ID = '" + currentDocId + "'";
        var existingData = MagicFindBySql("magic", checkSql);

        var shouldCreate = true;
        if (existingData && existingData.get("count") > 0) {
            debug("已存在提醒记录，跳过创建");
            shouldCreate = false;
        }

        if (shouldCreate) {
    
    // 3. 获取系统对象
    var user = GetAssistant();
    var applicationId = getApplication();
    var docProcess = getDocProcess(applicationId);
    
    // 4. 生成编号
    var billNo = countNext2("PSMT-", true, true, true, 4);
    
    // 5. 获取目标表单
    var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
    var listForm = formProcess.doViewByFormName("tlk_psm_01_personal_affairs_management_list", applicationId);
    
    // 6. 创建新记录
    var params = createParamsTable();
    var listDoc = docProcess.doNew(listForm, user, params);
    
    // 7. 从查询结果设置字段值
    listDoc.addStringItem("编号", billNo);

    // 事务标题 - 尝试多种可能的字段名
    var title = sourceData.get("ITEM_事务标题") ||
               sourceData.get("ITEM_标题") ||
               sourceData.get("ITEM_事务名称") || "个人事务";
    listDoc.addStringItem("事务标题", title);
    debug("设置事务标题: " + title);

    // 内容 - 尝试多种可能的字段名
    var content = sourceData.get("ITEM_事务描述") ||
                 sourceData.get("ITEM_备注") ||
                 sourceData.get("ITEM_内容") ||
                 sourceData.get("ITEM_说明") || "";
    listDoc.addStringItem("内容", content);
    debug("设置内容: " + content);

    // 合规提醒
    var compliance = sourceData.get("ITEM_合规提醒") || "是";
    listDoc.addStringItem("合规提醒", compliance);

    // 关联关系
    listDoc.addStringItem("关联事务ID", currentDocId);
    var sourceNumber = sourceData.get("ITEM_编号") || "";
    listDoc.addStringItem("关联事务编号", sourceNumber);
    debug("设置关联信息 - ID: " + currentDocId + ", 编号: " + sourceNumber);
    
            // 8. 创建记录
            docProcess.doCreate(listDoc);
            debug("成功创建提醒记录: " + billNo);
        }
    } else {
        debug("未找到源数据，文档ID: " + currentDocId);
    }

} catch (e) {
    debug("创建提醒记录失败: " + e.message);
}
