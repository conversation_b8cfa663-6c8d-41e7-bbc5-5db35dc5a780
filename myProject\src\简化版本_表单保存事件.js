

#include "SysUserUtils";

(function() {
    try {
        // 检查当前文档是否为个人事务管理表
        if (!doc || doc.getFormname() !== "tlk_psm_01_personal_affairs_management") {
            return;
        }

        // 获取系统对象
        var user = GetAssistant();
        var applicationId = getApplication();
        var docProcess = getDocProcess(applicationId);

        // 生成编号
        var billNo = countNext2("PSMT-", true, true, true, 4);

        // 获取list表单
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var listForm = formProcess.doViewByFormName("tlk_psm_01_personal_affairs_management_list", applicationId);

        // 创建新记录
        var params = createParamsTable();
        var listDoc = docProcess.doNew(listForm, user, params);

        // 设置字段值
        listDoc.addStringItem("编号", billNo);
        listDoc.addStringItem("创建人", user.getName());
        listDoc.addDateItem("创建日期", getToday());
        listDoc.addStringItem("关联事务ID", doc.getId());

        // 复制主要字段
        var title = doc.getItemValueAsString("事务标题") || doc.getItemValueAsString("标题") || "个人事务";
        listDoc.addStringItem("事务标题", title);

        var type = doc.getItemValueAsString("事务类型") || "一般事务";
        listDoc.addStringItem("事务类型", type);

        var priority = doc.getItemValueAsString("优先级") || "普通";
        listDoc.addStringItem("优先级", priority);

        // 创建记录
        docProcess.doCreate(listDoc);

    } catch (e) {
        // 错误不影响原始保存
        debug("创建list记录失败: " + e.message);
    }
})();



