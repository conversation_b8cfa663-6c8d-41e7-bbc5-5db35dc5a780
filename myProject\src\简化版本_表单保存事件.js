

// 引入系统工具函数
#include "SysUserUtils";

// 主要执行逻辑
(function() {
    try {
        // 检查当前文档是否为个人事务管理表
        if (!doc || doc.getFormname() !== "tlk_psm_01_personal_affairs_management") {
            return; // 不是目标表单，直接返回
        }
        
        // 检查是否已存在提醒记录（避免重复创建）
        var existingSql = "SELECT COUNT(*) as count FROM tlk_psm_01_personal_affairs_management_list " +
                         "WHERE ITEM_关联事务ID = '" + doc.getId() + "'";
        var existingData = MagicFindBySql("magic", existingSql);
        if (existingData && existingData.get("count") > 0) {
            debug("已存在提醒记录，跳过创建");
            return;
        }
        
        // 获取系统对象
        var user = GetAssistant();
        var applicationId = getApplication();
        var docProcess = getDocProcess(applicationId);
        
        // 生成提醒记录编号
        var reminderBillNo = countNext2("PSMT-", true, true, true, 4);
        
        // 获取提醒列表表单
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var listForm = formProcess.doViewByFormName("tlk_psm_01_personal_affairs_management_list", applicationId);
        
        if (!listForm) {
            debug("未找到提醒列表表单");
            return;
        }
        
        // 创建新的提醒记录
        var params = createParamsTable();
        var reminderDoc = docProcess.doNew(listForm, user, params);
        
        // 设置基本信息
        reminderDoc.addStringItem("编号", reminderBillNo);
        reminderDoc.addStringItem("创建人", user.getName());
        reminderDoc.addDateItem("创建日期", getToday());
        
        // 设置关联信息
        reminderDoc.addStringItem("关联事务ID", doc.getId());
        reminderDoc.addStringItem("关联事务编号", safeGetString(doc, "编号"));
        
        // 复制事务信息
        reminderDoc.addStringItem("事务标题", 
            safeGetString(doc, "事务标题") || 
            safeGetString(doc, "标题") || 
            safeGetString(doc, "事务名称") || 
            "个人事务提醒");
            
        reminderDoc.addStringItem("事务类型", safeGetString(doc, "事务类型") || "一般事务");
        reminderDoc.addStringItem("优先级", safeGetString(doc, "优先级") || "普通");
        reminderDoc.addStringItem("事务描述", 
            safeGetString(doc, "事务描述") || 
            safeGetString(doc, "备注") || 
            safeGetString(doc, "说明"));
            
        // 负责人信息
        var responsible = safeGetString(doc, "负责人") || 
                         safeGetString(doc, "经办人") || 
                         safeGetString(doc, "处理人");
        if (responsible) {
            reminderDoc.addStringItem("负责人", responsible);
        }
        
        // 部门信息
        var department = safeGetString(doc, "部门") || safeGetString(doc, "所属部门");
        if (department) {
            reminderDoc.addStringItem("部门", department);
        }
        
        // 设置提醒时间
        var planDate = safeGetDate(doc, "计划完成时间") || 
                      safeGetDate(doc, "截止时间") ||
                      safeGetDate(doc, "完成日期");
        
        if (planDate) {
            reminderDoc.addDateItem("计划完成时间", planDate);
            // 提前1天提醒
            var reminderDate = adjustDay(planDate, -1);
            reminderDoc.addDateItem("提醒时间", reminderDate);
        } else {
            // 默认明天提醒
            var defaultReminderDate = adjustDay(getToday(), 1);
            reminderDoc.addDateItem("提醒时间", defaultReminderDate);
        }
        
        // 设置提醒状态
        reminderDoc.addStringItem("提醒状态", "待提醒");
        reminderDoc.addStringItem("是否已提醒", "否");
        reminderDoc.addStringItem("提醒方式", "系统通知");
        reminderDoc.addDoubleItem("提醒次数", 0);
        
        // 创建提醒记录
        docProcess.doCreate(reminderDoc);
        debug("成功创建个人事务提醒记录: " + reminderBillNo);
        
    } catch (e) {
        debug("创建个人事务提醒记录失败: " + e.message);
        // 记录错误但不影响原始保存操作
        Packages.java.lang.System.out.println("Personal affairs reminder creation error: " + e);
    }
})();

/**
 * 安全获取字符串字段值
 * @param {Object} doc - 文档对象
 * @param {string} fieldName - 字段名
 * @returns {string} 字段值，获取失败返回空字符串
 */
function safeGetString(doc, fieldName) {
    try {
        return doc.getItemValueAsString(fieldName) || "";
    } catch (e) {
        return "";
    }
}

/**
 * 安全获取日期字段值
 * @param {Object} doc - 文档对象
 * @param {string} fieldName - 字段名
 * @returns {Date} 日期值，获取失败返回null
 */
function safeGetDate(doc, fieldName) {
    try {
        return doc.getItemValueAsDate(fieldName);
    } catch (e) {
        return null;
    }
}
