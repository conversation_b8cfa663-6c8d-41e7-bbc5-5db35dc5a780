

#include "SysUserUtils";

(function() {
    try {
        // 检查当前文档是否为个人事务管理表
        if (!doc || doc.getFormname() !== "tlk_psm_01_personal_affairs_management") {
            return;
        }

        // 获取系统对象
        var user = GetAssistant();
        var applicationId = getApplication();
        var docProcess = getDocProcess(applicationId);

        // 生成编号
        var billNo = countNext2("PSMT-", true, true, true, 4);

        // 获取list表单
        var formProcess = new Packages.cn.myapps.designtime.form.service.FormDesignTimeServiceImpl();
        var listForm = formProcess.doViewByFormName("tlk_psm_01_personal_affairs_management_list", applicationId);

        // 创建新记录
        var params = createParamsTable();
        var listDoc = docProcess.doNew(listForm, user, params);

        // 根据截图字段设置值
        listDoc.addStringItem("编号", billNo);

        // 事务标题 - 从原表复制
        var title = doc.getItemValueAsString("事务标题") ||
                   doc.getItemValueAsString("标题") ||
                   doc.getItemValueAsString("事务名称") || "个人事务";
        listDoc.addStringItem("事务标题", title);

        // 内容 - 可以是事务描述或备注
        var content = doc.getItemValueAsString("事务描述") ||
                     doc.getItemValueAsString("备注") ||
                     doc.getItemValueAsString("内容") ||
                     doc.getItemValueAsString("说明") || "";
        listDoc.addStringItem("内容", content);

        // 合规提醒 - 可以根据业务逻辑设置
        var compliance = doc.getItemValueAsString("合规提醒") || "是";
        listDoc.addStringItem("合规提醒", compliance);

        // 创建记录
        docProcess.doCreate(listDoc);
        debug("成功创建提醒记录: " + billNo);

    } catch (e) {
        // 错误不影响原始保存
        debug("创建list记录失败: " + e.message);
    }
})();



